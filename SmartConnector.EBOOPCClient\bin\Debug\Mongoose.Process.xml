<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Mongoose.Process</name>
    </assembly>
    <members>
        <member name="T:Mongoose.Process.Ews.AlarmOccurEvent">
            <summary>
            Models an occurence of Value and/or State change in a ValueItem
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.HierarchyChangeEvent">
            <summary>
            Models an occurence of a Hierarchy change event
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.SubscriptionReader">
            <summary>
            An EwsDataReader class that enables the reading of Subscriptions.
            </summary>
            <remarks>
            Either a subscription Id to read an existing Subscription, or a list of Ids to create a new one (and read it) can be passed into the class.
            This reader uses the InMemoryCache to store the NotificationId, so there is no need to pass this into the reader.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.SubscriptionId">
            <summary>
            The subscription Id of the subscription to be read, blank if there is no subscription yet
            </summary>
            <remarks>
            If SubscriptionId is set, the reader will attempt to read that subscription, and will fallback to 
            subscribe to the set Ids if this fails
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.SubscribedItems">
            <summary>
            Keeps a list of successfully subscribed item Ids when a subscription is made, otherwise empty
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.FailedSubscribedItems">
            <summary>
            Keeps a list of failed subscribe item Ids when a subscription is made, otherwise empty
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.SubsciptionChanged">
            <summary>
            If the Subscription Id changed when the read occurred, this is set to true.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.IsResubscribeRequired">
            <summary>
            If for some reason the Subscription could not be renewed, and it was not possible to use the same Ids this is set to true. Manual resubscribe needed.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.SubscriptionCacheTimeout">
            <summary>
            The amount of time (in minutes) before the subscription information will expire from the cache. However, this expiration will only occur if
            the subscription passed into the reader has not been read for SubscriptionCacheTimeout minutes.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.TimeRemainingBeforeRenew">
            <summary>
            The amount of time (in minutes) left in an existing subscription before an automatic renew will be performed.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.Ids">
            <summary>
            The list of ValueItem, AlarmEvent, SystemEvent, or Hierarchy Items to subscribe to
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.PriorityFrom">
            <summary>
            Lower limit when filtering by Priority.  Leave NULL to ignore.
            </summary>
            <remarks>
            Some EWS endpoints do not support open ended filter ranges.  Consult the documentation for your specific EWS server referenced.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.PriorityTo">
            <summary>
            Upper limit when filtering by Priority.  Leave NULL to ignore.
            </summary>
            <remarks>
            Some EWS endpoints do not support open ended filter ranges.  Consult the documentation for your specific EWS server referenced.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.TypesFilter">
            <summary>
            List of AlarmEventType or SystemEventType values to filter.  Leave empty to ignore the filter.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.ExpirationInMinutes">
            <summary>
            Sets the requested expiration in minutes for the the Subscription.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.EventIdTypeMode">
            <summary>
            The EventIdType that the list of Ids represent (Items, or ContainerItems).
            </summary>
            <remarks>
            EwsEventIdTypeModeEnum.ItemId if the Ids are that of actual ValueItems,
            EwsEventIdTypeModeEnum.ContainerItemId if the Ids are that of ContainerItems and you want all first level children
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.MaxPageCount">
            <summary>
            The maximum amount of pages the SubcriptionReader will read before it generates an error.
            </summary>
            <remarks>
            This is to prevent the case where the server returns an indefinite amount of pages
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.SubscriptionEventType">
            <summary>
            Defines the SubscriptionEventing type, ValueItemChanged, SystemEventChanged, AlarmEventChanged, HierarchyChanged
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.CachedSubscribedItems">
            <summary>
            The items that were successfully subscribed to when the subscription was made
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.SubscriptionReader.NotificationId">
            <summary>
            The notification Id of the current subscription to be read, blank if there is no subscription, or the user wants to start over
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionReader.TrySubscribe">
            <summary>
            Attemps to Subscribe, if the subscribe fails an error is generated.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionReader.TryRenew">
            <summary>
            Attempts to renew a subscription, if the renew fails then a new subscription is attempted.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionReader.TryUnsubscribe">
            <summary>
            Attempts to Unsubscribe the given Subscription Id from the sever
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionReader.IsRenewRequired">
            <summary>
            If the Subscription isn't in the cache or has less than 5 minutes remaining, return True, else return false. 
            </summary>
            <returns></returns>
        </member>
        <member name="T:Mongoose.Process.Ews.SubscriptionResultItem">
            <summary>
            A result item returned from the Subscription Reader
            </summary>
            <remarks>
            This ResultItem works differently than the other Result Item types, as it is a collection of potential Result Items types.
            When the ResultItem is intantiated only a single Property will be configured (e.g. ValueItemChangeEvent) and when the user attempts
            to access this result item, they must choose the type they have used. This can be infered by the EwsSubscriptionEventType that 
            was used to call the SubscriptionReader class)
            </remarks>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionResultItem.#ctor(Mongoose.Process.Ews.AlarmOccurEvent)">
            <summary>
            This constructor is used to add a AlarmItemChangeEvent to the Result Item
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionResultItem.#ctor(Mongoose.Process.Ews.ValueItemChangeEvent)">
            <summary>
            This constructor is used to add a ValueItemChangeEvent to the Result Item
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionResultItem.#ctor(Mongoose.Process.Ews.HierarchyChangeEvent)">
            <summary>
            This constructor is used to add a HierarchyChangeEvent to the Result Item
            </summary>
        </member>
        <member name="M:Mongoose.Process.Ews.SubscriptionResultItem.#ctor(Mongoose.Process.Ews.SystemEventEvent)">
            <summary>
            This constructor is used to add a SystemEventEvent to the Result Item
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.SystemEventEvent">
            <summary>
            Models an occurence of a SystemEvent
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.ValueItemChangeEvent">
            <summary>
            Models an occurence of Value and/or State change in a ValueItem
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.AlarmItemReader.PriorityFrom">
            <summary>
            Lower limit when filtering by Priority.  Leave NULL to ignore.
            </summary>
            <remarks>
            Some EWS endpoints do not support openended filter ranges.  Consult the documentation for your specific EWS server referenced.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.AlarmItemReader.PriorityTo">
            <summary>
            Upper limit when filtering by Priority.  Leave NULL to ignore.
            </summary>
            <remarks>
            Some EWS endpoints do not support openended filter ranges.  Consult the documentation for your specific EWS server referenced.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Ews.AlarmItemReader.AlarmTypesFilter">
            <summary>
            List of AlarmEventType values to filter against.  Leave empty but non-null to ignore the filter.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.AlarmItemReader.LastUpdate">
            <summary>
            Server sourced indicator of the last update called.  If NULL, a GetAlarmEvents call will be made otherwise a GetUpdatedAlarmEvents call will be made.
            This value will be updated by the reader and should be persisted and re-used for subsequent calls.
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.EwsServerConnection">
            <summary>
            Wrapper class for the properties needed to connect to a SmartConnector EWS Serve endpoint
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.EwsServerConnection.ServerName">
            <summary>
            Logical name of the server to connect to.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.EwsServerConnection.UserName">
            <summary>
            User name to authenticate against.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.EwsServerConnection.Password">
            <summary>
            Password to authenticate against.
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.HistoryItemReader">
            <summary>
            Reads the history of the supplied datapoints from the supplied range of time.  Handles all paging issues and returns all history for all items over their range.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.HistoryItemReader.TimeFrom">
            <summary>
            The DateTime to read history from.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.HistoryItemReader.TimeTo">
            <summary>
            The DateTime to read history to.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.HistoryItemReader.MoreDataRef">
            <summary>
            Used by the client if the target EWS server supports EwsGetHistoryMoreDataRefMode.SequenceNumber. 
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.HistoryItemReader.HistoryItemsToRead">
            <summary>
            Explicit HistoryItem IDs to read.  At least one value is required.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.HistoryReadResultItem.FinalResponseMoreDataRef">
            <summary>
            The MoreDataRef returned in the last page of the response.
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.EwsDataReader`1">
            <summary>
            Base class for reading data from an EWS server via web services
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.EwsDataReader`1.EwsConnection">
            <summary>
            Returns a dependency injected instance of IManagedEwsClient class.  
            </summary>
        </member>
        <member name="T:Mongoose.Process.Ews.EwsDataWriter`1">
            <summary>
            Base class for writing data to an EWS server via web services
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.EwsDataWriter`1.EwsConnection">
            <summary>
            Returns a dependency injected instance of IManagedEwsClient class.  
            </summary>
        </member>
        <member name="P:Mongoose.Process.Ews.ValueItemReader.DataPointsToRead">
            <summary>
            The input of what points are going to be read.
            </summary>
        </member>
        <member name="T:Mongoose.Process.IStaThreadedProcessor">
            <summary>
            Used to denote a Processor that must be run in an STA thread.  
            </summary>
        </member>
        <member name="T:Mongoose.Process.LicensedNullProcessor">
            <summary>
            Sample Processor which requires a license in order to validate.
            </summary>
        </member>
        <member name="P:Mongoose.Process.LicensedNullProcessor.IsLicensed">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Process.ILongRunningProcess">
            <summary>
            Used to denote a Processor that runs until commanded to stop.
            </summary>
        </member>
        <member name="T:Mongoose.Process.LongRunningProcessor">
            <summary>
            Sample processor which will start and idle until commanded to stop.
            </summary>
        </member>
        <member name="P:Mongoose.Process.DataPoint.Id">
            <summary>
            Id for the DataPoint in the sourced system
            </summary>
        </member>
        <member name="P:Mongoose.Process.DataPoint.Name">
            <summary>
            Alias name given to the DataPoint.  Can be used to unambiguously define a point.
            </summary>
        </member>
        <member name="P:Mongoose.Process.DataPointValue.Value">
            <summary>
            Value of the DataPointpa
            </summary>
        </member>
        <member name="P:Mongoose.Process.DataPointValue.ValueType">
            <summary>
            The value type 
            </summary>
        </member>
        <member name="P:Mongoose.Process.DataPointValueResultItem.Success">
            <summary>
            Indicates if the Value was successfully read.
            </summary>
        </member>
        <member name="T:Mongoose.Process.Processor">
            <summary>
            Implementation of the "processing" logic by using any combination of DataReader and/or DataWriter combined with sub-class provided logic to formulate a result.
            </summary>
            <remarks>
            This class' execution is inteded to be aysnchronous.
            </remarks>
        </member>
        <member name="P:Mongoose.Process.Processor.Name">
            <summary>
            The name of the Configuration that instantiated this Processor
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.ConfigurationId">
            <summary>
            The Id of the ProcessConfiguration which hydrated this Processor.  Available to sub-classes as a convenience.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.IsCancellationRequested">
            <summary>
            Helper so any class implementing IProcessor can check the cancellation token without needed direct access to it.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.CancellationToken">
            <summary>
            The CancellationToken supplied at Execute.  We make it available so any other asynchronous tasks can leverage the common token.  See CheckCancellationToken
            </summary>
        </member>
        <member name="M:Mongoose.Process.Processor.CheckCancellationToken">
            <summary>
            Call this method to check the cancellation token.  If cancellation is requested, this method will call CleanupBeforeCancellation prior to throwing the OperationCanceledException
            </summary>
            <remarks>
            If this Processor is running within an aysynch Task (as in the case of a WorkerThread) the actual exception will not be thrown until a call is made to fetch the Results and in that case, it
            will actually be an AggregateException.
            </remarks>
        </member>
        <member name="M:Mongoose.Process.Processor.CleanupBeforeCancellation">
            <summary>
            If sub-classes need to do any "cleanup" before throwing the CancellationRequested exception, that code would go in this overridden method.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Processor.Execute(System.Threading.CancellationToken,Mongoose.Process.ProcessorExecutionMode)">
            <summary>
            External entry point to processor.  This method provides entry to the Validation (both schema and sub-classes) and then execution with cancellation support.
            </summary>
            <param name="token">CancellationToken provided by the WorkerThread instance to cancel the async execution.</param>
            <param name="mode">The mode the Processor is executing under.</param>
            <returns>Results as a ProcessorReset</returns>
        </member>
        <member name="M:Mongoose.Process.Processor.Execute_Subclass">
            <summary>
            Sub-class specific code for the Processor.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Process.Processor.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            Called during ValidateInstance prior to execution.  Override only if your implementation requires custom validation.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Processor.OnValidateInstanceCompleted(System.Collections.Generic.List{Mongoose.Common.Prompt})">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Process.Processor.IsLicensed">
            <summary>
            Indicates to the runtime framework whether the author has requested Extension License enforcement.
            </summary>
            <remarks>
            If you wish to exclude licensing support for this Processor in your assembly, override and return false.
            </remarks>
        </member>
        <member name="M:Mongoose.Process.Processor.ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense)">
            <summary>
            Custom feature license enforcement is up to the implementing extension.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.ActionBroker">
            <summary>
            Lazily instantiated source for starting and stopping other Processors and/or EWS Servers.  
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.Cache">
            <summary>
            Cross thread accessable in memory cache.  Everyting, including reference types, are serialized into the cache.  This means that an AddItem followed by a RetrieveItem will
            never return the same instance of item.  This is intentional to prevent cross thread access issues.  All methods lock the internal storage for the duration of the call.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Processor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Processor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="M:Mongoose.Process.Processor.NoBusyWait(System.Int64,System.Boolean)">
            <summary>
            Waits for some a fixed duration (in mSec) but will monitor the cancellation token.
            If throwIfCancellationIsRequested is true, will throw OperationCancelledException if cancellation is requested otherwise will just exit the "wait".
            </summary>
            <param name="duration">Duration to wait for (in mSec)</param>
            <param name="throwIfCancellationIsRequested">Indicates how the cancellation token will be monitored during the wait.</param>
        </member>
        <member name="T:Mongoose.Process.Queue.Queue">
            <summary>
            Defines an MSMQ queue that it can be lazily referenced/created.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Queue.Queue.Name">
            <summary>
            Queue Name. Example format is ".\Private$\MyPushProcessor"
            </summary>
        </member>
        <member name="P:Mongoose.Process.Queue.Queue.ModifiedName">
            <summary>
            Queue name where any reference to a ".\" prefix is replaced by the machine name.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Queue.Queue.MessageQueue">
            <summary>
            Returns a reference to the actual MSMQ queue.  The queue will be creted if it doesn't already exist.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Queue.Queue.EnsureQueueExists">
            <summary>
            Creates our reference to the MSMQ instance, creating the actual queue if necessary.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Queue.Queue.AddToQueue``1(``0)">
            <summary>
            Pushes the supplied event onto the queue in transactional manner
            </summary>
        </member>
        <member name="T:Mongoose.Process.Queue.QueueProcessorBase`1">
            <summary>
            Base processor for a processor which leverates an MSMQ to either push/pop data from.
            See https://msdn.microsoft.com/en-us/library/aa967729(v=vs.110).aspx to install MSMQ on your development or production machine.
            </summary>
            <remarks>
            For now, we're going with a transactional queue but if Prioritization of messages is more important, we'll need to reconsider that.
            See http://geekswithblogs.net/Plumbersmate/archive/2011/02/03/ldquowhy-do-transactional-messages-all-have-the-same-priorityrdquo.aspx
            </remarks>
        </member>
        <member name="M:Mongoose.Process.Queue.QueueProcessorBase`1.OnValidateInstanceCompleted(System.Collections.Generic.List{Mongoose.Common.Prompt})">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Process.Queue.QueueProcessorBase`1.AddToQueue(`0)">
            <summary>
            Pushes the supplied event onto the queue in transactional manner
            </summary>
        </member>
        <member name="M:Mongoose.Process.Queue.QueueProcessorBase`1.OnPeekCompleted(System.Object,System.Messaging.PeekCompletedEventArgs)">
            <summary>
            Sub-classes must provide a method to peek into the queue if they choose to add an event handler to Queue.MessageQueue for "peeking".
            </summary>
        </member>
        <member name="T:Mongoose.Process.Smtp.SmtpEmail">
            <summary>
            Layers in the structure of an email.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpEmail.Subject">
            <summary>
            The subject that will be given to all email messages emanating from this processor.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpEmail.To">
            <summary>
            List of email addresses for recipients of emails from this Processsor
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpEmail.Cc">
            <summary>
            List of email addresses for recipients of emails from this Processsor
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpEmail.Bcc">
            <summary>
            List of email addresses for recipients of emails from this Processsor
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpEmail.FromAddress">
            <summary>
            Email address which will be used as the from address.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Smtp.SmtpEmail.SendEmail">
            <summary>
            Sends the email as defined by the properties in this class.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.UserName">
            <summary>
            The user on the SMTP server which we will authenticate against.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.Password">
            <summary>
            The password for the the UserName on the SMTP server which we will authenticate against.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.SmtpServerAddress">
            <summary>
            The address of the SMTP server.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.SmtpServerPort">
            <summary>
            The port on the SMTP server we will use.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.MailDeliveryMethod">
            <summary>
            The SMTP delivery method we will use.  Typically this will be Network which is the default
            </summary>
        </member>
        <member name="P:Mongoose.Process.Smtp.SmtpMailer.Client">
            <summary>
            Lazy instantiated SmtpClient based on the properties of this class.  If a property is changed, this will be automatically reset and recreated when needed.
            </summary>
        </member>
        <member name="M:Mongoose.Process.Smtp.SmtpMailer.SendEmail(System.Net.Mail.MailMessage)">
            <summary>
            Sends the supplied email message.
            </summary>
        </member>
        <member name="T:Mongoose.Process.TimeSeriesData">
            <summary>
            Data class to represent a tuple of Value, Time and a State.
            </summary>
        </member>
        <member name="P:Mongoose.Process.TimeSeriesData.Value">
            <summary>
            Value of data getting pushed.
            </summary>
        </member>
        <member name="P:Mongoose.Process.TimeSeriesData.Type">
            <summary>
            Type of data getting pushed.
            </summary>
        </member>
        <member name="P:Mongoose.Process.TimeSeriesData.Timestamp">
            <summary>
            Occurrence of the Value.
            </summary>
        </member>
        <member name="P:Mongoose.Process.TimeSeriesData.State">
            <summary>
            State indicator.  Ostensibly EwsValueStateEnum but it doesn't need to be.
            </summary>
        </member>
        <member name="P:Mongoose.Process.TimeSeriesData.Units">
            <summary>
            Units for the data.
            </summary>
        </member>
        <member name="M:Mongoose.Process.TimeSeriesData.InferTypeFromValue(System.String)">
            <summary>
            Given a possible Type value, infer what it's enumeration value is.  Check is case insensitive.
            </summary>
        </member>
        <member name="M:Mongoose.Process.ExtensionMethods.CopyValues(System.Collections.Generic.List{Mongoose.Process.DataPointValue},System.Collections.Generic.List{Mongoose.Process.DataPointValue})">
            <summary>
            Copies the value from each input item to an output item with the matching name.
            </summary>
            <param name="input">List of DataPointValue which are the source of the copy</param>
            <param name="output">List of DataPointValue which is the destination of the copy</param>
        </member>
        <member name="M:Mongoose.Process.ExtensionMethods.CopyValues(System.Collections.Generic.List{Mongoose.Process.DataPointValueResultItem},System.Collections.Generic.List{Mongoose.Process.DataPointValue})">
            <summary>
            Copies the value from each input item to an output item with the matching name.
            </summary>
            <param name="input">List of DataPointValue which are the source of the copy</param>
            <param name="output">List of DataPointValue which is the destination of the copy</param>
        </member>
        <member name="T:Mongoose.Process.NullProcessor">
            <summary>
            Sample Processor that does nothing of note except sleep for a finite time frame and then ends.
            </summary>
        </member>
        <member name="P:Mongoose.Process.NullProcessor.SleepDuration">
            <summary>
            Sleep duration in mSec
            </summary>
        </member>
        <member name="P:Mongoose.Process.NullProcessor.ThrowException">
            <summary>
            Alternative to "normal" behavior (sleeping) if set to true, this property will cause the processor to throw a runtime exception.
            Useful for confirming Exception handling, logging and subsequent activity.
            </summary>
        </member>
        <member name="T:Mongoose.Process.ProcessorResult">
            <summary>
            Results of a Processor execution
            </summary>
        </member>
        <member name="T:Mongoose.Process.Resources.Prompts">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.AlarmItemReaderMaxLoopCountExceeded">
            <summary>
              Looks up a localized string similar to AlarmItemReady exceeded the maximum loop count.  This could be caused by excessive paging in the EWS Server or bad results.  Consult the log files for details..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.FailedToReadPoint">
            <summary>
              Looks up a localized string similar to The point {0} could not be read because the endpoint returned {1}..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.FailedToWritePoint">
            <summary>
              Looks up a localized string similar to The point {0} could not be written because the endpoint returned {1}..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.GetAlarmEventsNeedsRefresh">
            <summary>
              Looks up a localized string similar to GetAlarmEvents returned NeedsRefresh.  Consult your EWS server system documentation for proper remedy..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.ImplicitSSLPrompt">
            <summary>
              Looks up a localized string similar to Verify that the port you are using supports explicit SSL.  SMTP over implicit SSL is not supported..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.InvalidState_MultiCollection">
            <summary>
              Looks up a localized string similar to At least one member is required..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.MessageQueueCreationError">
            <summary>
              Looks up a localized string similar to Queue could not locate or create the MSMQ referenced..
            </summary>
        </member>
        <member name="P:Mongoose.Process.Resources.Prompts.ProcessorCancelledByWorkerManager">
            <summary>
              Looks up a localized string similar to Cancelled by the Worker Manager..
            </summary>
        </member>
    </members>
</doc>
