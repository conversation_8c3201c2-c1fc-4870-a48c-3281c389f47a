﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000017 RID: 23
	public class Wind
	{
		// Token: 0x17000030 RID: 48
		// (get) Token: 0x060000C0 RID: 192 RVA: 0x00008338 File Offset: 0x00006538
		// (set) Token: 0x060000C1 RID: 193 RVA: 0x00008340 File Offset: 0x00006540
		[JsonProperty("speed")]
		public double Speed { get; set; }

		// Token: 0x17000031 RID: 49
		// (get) Token: 0x060000C2 RID: 194 RVA: 0x00008349 File Offset: 0x00006549
		// (set) Token: 0x060000C3 RID: 195 RVA: 0x00008351 File Offset: 0x00006551
		[JsonProperty("deg")]
		public double Deg { get; set; }
	}
}
