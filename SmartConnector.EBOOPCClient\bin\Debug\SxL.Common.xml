<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SxL.Common</name>
    </assembly>
    <members>
        <member name="T:SxL.Common.ActivatorHelper">
            <summary>
            Collection of helper methods for the Activator class.
            </summary>
        </member>
        <member name="M:SxL.Common.ActivatorHelper.ActivateObject``1(System.String,System.String,System.Object[])">
            <summary>
            Instantiates an instance of the assemblyFile/className supplied.  Uses optional constructorParameters if supplied.
            </summary>
        </member>
        <member name="M:SxL.Common.ActivatorHelper.ActivateObject(System.String,System.String,System.Type@,System.Object[])">
            <summary>
            Instantiates an instance of the assemblyFile/className supplied.  Uses optional constructorParameters if supplied.
            </summary>
        </member>
        <member name="M:SxL.Common.ActivatorHelper.ExtractTypeOfObject(System.String,System.String)">
            <summary>
            Attempts to extract the Type object from the assemblyFile and className values.  
            </summary>
        </member>
        <member name="T:SxL.Common.Crypto">
            <summary>
            A set of static helpers for a variety of cryptographic operations
            </summary>
        </member>
        <member name="M:SxL.Common.Crypto.CalculateHash(System.String)">
            <summary>
            Returns a Base64String representation of the SHA512Managed hash of value.
            </summary>
        </member>
        <member name="M:SxL.Common.Crypto.GenerateCryptoKey">
            <summary>
            Generates a random 256 byte HEX string encoded key value.
            </summary>
        </member>
        <member name="T:SxL.Common.Endpoint">
            <inheritdoc />
        </member>
        <member name="P:SxL.Common.Endpoint.Address">
            <inheritdoc />
        </member>
        <member name="P:SxL.Common.Endpoint.UserName">
            <inheritdoc />
        </member>
        <member name="P:SxL.Common.Endpoint.Password">
            <inheritdoc />
        </member>
        <member name="M:SxL.Common.ExtensionMethods.WeekOfYear(System.DateTime)">
            <summary>
            We're not using ISO rules here.  We're using standard "week starts on Sunday" rules.
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateLongTime(System.Nullable{System.DateTime})">
            <summary>
            Converts the supplied value to Local Time and returns ToString("G")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateLongTime(System.DateTime)">
            <summary>
            Converts the supplied value to Local Time and returns ToString("G")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateLongTime(System.Nullable{System.DateTimeOffset})">
            <summary>
            Converts the supplied value to Local Time and returns ToString("G")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateLongTime(System.DateTimeOffset)">
            <summary>
            Converts the supplied value to Local Time and returns ToString("G")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateShortTime(System.Nullable{System.DateTime})">
            <summary>
            Converts the supplied value to Local Time and returns ToString("g")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateShortTime(System.DateTime)">
            <summary>
            Converts the supplied value to Local Time and returns ToString("g")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateShortTime(System.Nullable{System.DateTimeOffset})">
            <summary>
            Converts the supplied value to Local Time and returns ToString("g")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToLocalShortDateShortTime(System.DateTimeOffset)">
            <summary>
            Converts the supplied value to Local Time and returns ToString("g")
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToMomentString(System.Nullable{System.DateTime})">
            <summary>
            Converts the supplied value to a string usable by Moment.js
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToMomentString(System.DateTime)">
            <summary>
            Converts the supplied value to a string usable by Moment.js
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToMomentString(System.Nullable{System.DateTimeOffset})">
            <summary>
            Converts the supplied value to a string usable by Moment.js
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToMomentString(System.DateTimeOffset)">
            <summary>
            Converts the supplied value to a string usable by Moment.js
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ToJSON(System.Object)">
            <summary>
            Returns a JSON representation of this object.
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.SplitPascalCase(System.String)">
            <summary>
            Helper to split a string by Pascal (or camal) casing.
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.TryToTimeSpan(System.String,System.TimeSpan@)">
            <summary>
            Helper to extract parse a W3C Duration into a TimeSpan instance.
            </summary>
            <remarks>
            See http://www.w3schools.com/schema/schema_dtypes_date.asp
            </remarks>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.IsValidUrl(System.String)">
            <summary>
            Returns true if anyUri is a valid URL value.  Will return true if it is either HTTP or HTTPS.  See also <see cref="T:System.Runtime.Remoting.Activation.UrlAttribute"/> for validation scenarios
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.GetValueOrDefault``2(System.Collections.Generic.Dictionary{``0,``1},``0)">
            <summary>
            Similar to "TryGetValue" but will return the default(TValue) if no value is present.
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.LoadValue(System.Security.SecureString,System.String)">
            <summary>
            Safely secures insecureString into secureString
            </summary>
        </member>
        <member name="M:SxL.Common.ExtensionMethods.ExtractValue(System.Security.SecureString)">
            <summary>
            Extracts the actual string contents from a SecureString instance
            </summary>
        </member>
        <member name="T:SxL.Common.IEndpoint">
            <summary>
            Common interface for any URL which requires authentication
            </summary>
        </member>
        <member name="P:SxL.Common.IEndpoint.Address">
            <summary>
            The Address of the endpoint
            </summary>
        </member>
        <member name="P:SxL.Common.IEndpoint.UserName">
            <summary>
            The UserName with which to authenticate
            </summary>
        </member>
        <member name="P:SxL.Common.IEndpoint.Password">
            <summary>
            The Password with which to authenticate
            </summary>
        </member>
        <member name="T:SxL.Common.ILoggerFilterProvider">
            <summary>
            Interface to define a provider of Log Filtering.
            </summary>
        </member>
        <member name="M:SxL.Common.ILoggerFilterProvider.AllowLogging(System.String,SxL.Common.LoggingLevel)">
            <summary>
            Return true to allow logging the supplied category.
            </summary>
        </member>
        <member name="P:SxL.Common.ILoggerFilterProvider.MaximumLoggingLevel">
            <summary>
            Returns the maximum level which will be logged.
            </summary>
        </member>
        <member name="M:SxL.Common.ILoggerFilterProvider.Refresh">
            <summary>
            Refreshes the provider explicitly.
            </summary>
        </member>
        <member name="T:SxL.Common.ITraversable">
            <summary>
            Identifies an entity as "Traverseable".  Used for deep validation reflective validation and value assignment.
            </summary>
        </member>
        <member name="T:SxL.Common.Logger">
            <summary>
            Centralized Logger class.  Utilizes the LoggerFilter singleton to control what actually gets logged to file - everything is written to the console.
            All logs are written to a Logs directory in the AppDomain.CurrentDomain.GetData("DataDirectory") value.  If this value is not set, logs will be written to the baseDir of the current
            application context.
            </summary>
        </member>
        <member name="P:SxL.Common.Logger.InternalLogger">
            <summary>
            The actual instance of the NLog logger.
            </summary>
        </member>
        <member name="P:SxL.Common.Logger.LogDestinationFolder">
            <summary>
            Location where log files will be written to.
            </summary>
        </member>
        <member name="P:SxL.Common.Logger.MaximumLoggingLevel">
            <summary>
            Returns the maximum level which will be logged.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogTrace(System.String,System.Object[])">
            <summary>
            Log at the Trace level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogStatus(System.String,System.Object[])">
            <summary>
            Log at the Status level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogInfo(System.String,System.Object[])">
            <summary>
            Log at the Info level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogDebug(System.String,System.Object[])">
            <summary>
            Log at the Debug level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogError(System.String,System.Object[])">
            <summary>
            Log at the Error level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.LogError(System.String,System.Exception,System.Object[])">
            <summary>
            Log at the Error level.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.Log(System.String,SxL.Common.LoggingLevel,System.Object[])">
            <summary>
            Common helper for all Log* methods.
            </summary>
        </member>
        <member name="M:SxL.Common.Logger.ElapsedTimeInMsec(System.DateTime,System.DateTime)">
            <summary>
            Returns a string like "ET=xx mSec"
            </summary>
        </member>
        <member name="T:SxL.Common.LoggingLevel">
            <summary>
            Logging levels for SxL.Logger
            </summary>
        </member>
        <member name="T:SxL.Common.LoggerFilter">
            <summary>
            Singleton logging filter control.  Uses a dependency injected FilterProvider to perform the filtering.  If no such provider is available, then no filtering will be performed (i.e. everything will be logged).
            </summary>
            <remarks>
            Rather than force anyone consuming this logger to reference StrutureMap, I opted to have filtering be a passive thing.  That way one could actually modify filters on the fly.  
            If this becomes an issue, then we can revisit this design.  For now it is the easiest way to implement.
            </remarks>
        </member>
        <member name="P:SxL.Common.LoggerFilter.Provider">
            <summary>
            Dependency injected ICategoryFilterProvider
            </summary>
        </member>
        <member name="T:SxL.Common.QueryBuilder">
            <summary>
            Helper to assemble a complete URL with query parameters
            </summary>
        </member>
        <member name="P:SxL.Common.QueryBuilder.BaseUrl">
            <summary>
            The base of the URL.  No validation is done so it is assumed that this is of the format http[s]://server[:port]/route
            </summary>
        </member>
        <member name="M:SxL.Common.QueryBuilder.AddQueryParameter``1(System.String,``0,System.Boolean)">
            <summary>
            Adds a parameter to the query.  If the paramater's name already exists, the new parmaeter will be added and the older one removed.
            Names are normalized to lower case by convention.  See https://tools.ietf.org/html/rfc3986#page-11.
            </summary>
        </member>
        <member name="M:SxL.Common.QueryBuilder.RemoveQueryParameter(System.String)">
            <summary>
            Removes the named query parameter.
            </summary>
        </member>
        <member name="M:SxL.Common.QueryBuilder.ClearParameters">
            <summary>
            Clears all parameters from the query but leaves the BaseUrl untouched.
            </summary>
        </member>
        <member name="P:SxL.Common.QueryBuilder.CompleteUrl">
            <summary>
            Returns the complete URL
            </summary>
        </member>
        <member name="T:SxL.Common.StringLock">
            <summary>
            Class that provides a lockable object based on a string value.
            </summary>
            <remarks>
            Thanks to http://stackoverflow.com/questions/4224888/locking-by-string-is-this-safe-sane
            </remarks>
        </member>
        <member name="T:SxL.Common.TempFileManager">
            <summary>
            Manages a list of temp file names and when disposed, deletes those temp files gracefully.
            Has some helper methods to automatically persist a serialized object in a temp file after pushing it onto the managed temp file stack.
            </summary>
        </member>
    </members>
</doc>
