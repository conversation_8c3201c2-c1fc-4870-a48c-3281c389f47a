﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AC94C88F-2C61-4F37-806F-191B9281F54A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>SmartConnector</RootNamespace>
    <AssemblyName>SmartConnector.EBOOPCClient</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AMS.Profile">
      <HintPath>C:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\AMS.Profile.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Common">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Ews.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Server.Contract">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Ews.Server.Contract.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Mongoose.Common">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Mongoose.Common.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Ews.Server">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Mongoose.Ews.Server.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Ews.Server.Data">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Mongoose.Ews.Server.Data.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Process">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\Mongoose.Process.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>C:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OpcDaNetUA.Net4">
      <HintPath>C:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\OpcDaNetUA.Net4.dll</HintPath>
    </Reference>
    <Reference Include="OpcNetBase.Net4">
      <HintPath>C:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\OpcNetBase.Net4.dll</HintPath>
    </Reference>
    <Reference Include="SxL.Common">
      <HintPath>C:\Program Files (x86)\Schneider Electric\SmartConnector\SxL.Common.dll</HintPath>
    </Reference>

    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Services" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EBOOPCClient\EBOOpcClientProcessorBase.cs" />
    <Compile Include="EBOOPCClient\EBOOpcItem.cs" />
    <Compile Include="EBOOPCClient\EwsServer\CustomDataExchange.cs" />
    <Compile Include="EBOOPCClient\EwsServer\CustomEwsServiceHost.cs" />

    <Compile Include="EBOOPCClient\EwsServer\CustomSetValuesProcessor.cs" />
    <Compile Include="EBOOPCClient\InitApp.cs" />
    <Compile Include="EBOOPCClient\KeyVal.cs" />
    <Compile Include="EBOOPCClient\OpcClientManager.cs" />
    <Compile Include="EBOOPCClient\Proxy\City.cs" />
    <Compile Include="EBOOPCClient\Proxy\Clouds.cs" />
    <Compile Include="EBOOPCClient\Proxy\Coord.cs" />
    <Compile Include="EBOOPCClient\Proxy\CurrentConditions.cs" />
    <Compile Include="EBOOPCClient\Proxy\Forecast.cs" />
    <Compile Include="EBOOPCClient\Proxy\List.cs" />
    <Compile Include="EBOOPCClient\Proxy\Main.cs" />
    <Compile Include="EBOOPCClient\Proxy\Rain.cs" />
    <Compile Include="EBOOPCClient\Proxy\Sys.cs" />
    <Compile Include="EBOOPCClient\Proxy\Weather.cs" />
    <Compile Include="EBOOPCClient\Proxy\Wind.cs" />
    <Compile Include="EBOOPCClient\ServerHelper.cs" />
    <Compile Include="EBOOPCClient\SetupProcessor.cs" />

    <Compile Include="EBOOPCClient\UpdateProcessor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Tools\ArrayHelper.cs" />
    <Compile Include="Tools\Global.cs" />
    <Compile Include="Tools\ILog.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>