﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000013 RID: 19
	public class Main
	{
		// Token: 0x17000022 RID: 34
		// (get) Token: 0x060000A0 RID: 160 RVA: 0x0000824A File Offset: 0x0000644A
		// (set) Token: 0x060000A1 RID: 161 RVA: 0x00008252 File Offset: 0x00006452
		[JsonProperty("temp")]
		public double Temp { get; set; }

		// Token: 0x17000023 RID: 35
		// (get) Token: 0x060000A2 RID: 162 RVA: 0x0000825B File Offset: 0x0000645B
		// (set) Token: 0x060000A3 RID: 163 RVA: 0x00008263 File Offset: 0x00006463
		[JsonProperty("temp_min")]
		public double TempMin { get; set; }

		// Token: 0x17000024 RID: 36
		// (get) Token: 0x060000A4 RID: 164 RVA: 0x0000826C File Offset: 0x0000646C
		// (set) Token: 0x060000A5 RID: 165 RVA: 0x00008274 File Offset: 0x00006474
		[JsonProperty("temp_max")]
		public double TempMax { get; set; }

		// Token: 0x17000025 RID: 37
		// (get) Token: 0x060000A6 RID: 166 RVA: 0x0000827D File Offset: 0x0000647D
		// (set) Token: 0x060000A7 RID: 167 RVA: 0x00008285 File Offset: 0x00006485
		[JsonProperty("pressure")]
		public double Pressure { get; set; }

		// Token: 0x17000026 RID: 38
		// (get) Token: 0x060000A8 RID: 168 RVA: 0x0000828E File Offset: 0x0000648E
		// (set) Token: 0x060000A9 RID: 169 RVA: 0x00008296 File Offset: 0x00006496
		[JsonProperty("sea_level")]
		public double SeaLevel { get; set; }

		// Token: 0x17000027 RID: 39
		// (get) Token: 0x060000AA RID: 170 RVA: 0x0000829F File Offset: 0x0000649F
		// (set) Token: 0x060000AB RID: 171 RVA: 0x000082A7 File Offset: 0x000064A7
		[JsonProperty("grnd_level")]
		public double GrndLevel { get; set; }

		// Token: 0x17000028 RID: 40
		// (get) Token: 0x060000AC RID: 172 RVA: 0x000082B0 File Offset: 0x000064B0
		// (set) Token: 0x060000AD RID: 173 RVA: 0x000082B8 File Offset: 0x000064B8
		[JsonProperty("humidity")]
		public int Humidity { get; set; }

		// Token: 0x17000029 RID: 41
		// (get) Token: 0x060000AE RID: 174 RVA: 0x000082C1 File Offset: 0x000064C1
		// (set) Token: 0x060000AF RID: 175 RVA: 0x000082C9 File Offset: 0x000064C9
		[JsonProperty("temp_kf")]
		public double TempKf { get; set; }
	}
}
