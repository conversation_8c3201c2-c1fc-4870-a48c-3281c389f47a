﻿using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using SmartConnector.EBOOPCClient;

namespace SmartConnector.Tools
{
	// Token: 0x02000003 RID: 3
	public static class Global
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x06000007 RID: 7 RVA: 0x00002428 File Offset: 0x00000628
		public static string AssemblyTitle
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyTitleAttribute), false);
				if (customAttributes.Length != 0)
				{
					AssemblyTitleAttribute assemblyTitleAttribute = (AssemblyTitleAttribute)customAttributes[0];
					if (assemblyTitleAttribute.Title != "")
					{
						return assemblyTitleAttribute.Title;
					}
				}
				return Path.GetFileNameWithoutExtension(Assembly.GetExecutingAssembly().CodeBase);
			}
		}

		// Token: 0x06000008 RID: 8 RVA: 0x00002480 File Offset: 0x00000680
		public static uint TickDiff(uint nCommapreTick)
		{
			int tickCount = Environment.TickCount;
			uint val = (uint)Math.Abs((long)((ulong)nCommapreTick - (ulong)((long)tickCount)));
			uint val2 = (uint)Math.Abs((long)tickCount - (long)((ulong)nCommapreTick));
			return Math.Min(val, val2);
		}

		// Token: 0x06000009 RID: 9 RVA: 0x000024B0 File Offset: 0x000006B0
		public static uint TickDiff(uint nCommapreTick1, uint nCommapreTick2)
		{
			uint val = (uint)Math.Abs((long)((ulong)(nCommapreTick2 - nCommapreTick1)));
			uint val2 = (uint)Math.Abs((long)((ulong)(nCommapreTick1 - nCommapreTick2)));
			return Math.Min(val, val2);
		}

		// Token: 0x0600000A RID: 10
		[DllImport("user32.dll")]
		public static extern int GetSystemMenu(int hwnd, int bRevert);

		// Token: 0x0600000B RID: 11
		[DllImport("user32.dll")]
		public static extern int AppendMenu(int hMenu, int Flagsw, int IDNewItem, string lpNewItem);

		// Token: 0x0600000C RID: 12
		[DllImport("kernel32.dll", EntryPoint = "GetSystemTime", SetLastError = true)]
		public static extern void Win32GetSystemTime(ref Global.SystemTime st);

		// Token: 0x0600000D RID: 13 RVA: 0x000024D8 File Offset: 0x000006D8
		public static bool DoPing(string strIP)
		{
			Ping ping = new Ping();
			PingOptions pingOptions = new PingOptions();
			pingOptions.DontFragment = true;
			string s = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
			byte[] bytes = Encoding.ASCII.GetBytes(s);
			int timeout = 1000;
			try
			{
				if (ping.Send(strIP, timeout, bytes, pingOptions).Status == IPStatus.Success)
				{
					return true;
				}
			}
			catch (Exception)
			{
			}
			return false;
		}

		// Token: 0x04000001 RID: 1
		public static string strLocation = "";

		// Token: 0x04000002 RID: 2
		public static string strAppPath = "";

		// Token: 0x04000003 RID: 3
		public static bool _ExitApp = false;

		// Token: 0x04000004 RID: 4
		public static UCMECheckLicense.UCMELicInfo _LicInfo = null;

		// Token: 0x04000005 RID: 5
		public static DateTime _StartTime = DateTime.Now;

		// Token: 0x04000006 RID: 6
		public static readonly int CURRENT_VERSION_NUMBER = 1;

		// Token: 0x04000007 RID: 7
		public static readonly int DEMO_TIME_IN_MINUTES = 120;

		// Token: 0x0200001D RID: 29
		public struct SystemTime
		{
			// Token: 0x04000098 RID: 152
			public ushort Year;

			// Token: 0x04000099 RID: 153
			public ushort Month;

			// Token: 0x0400009A RID: 154
			public ushort DayOfWeek;

			// Token: 0x0400009B RID: 155
			public ushort Day;

			// Token: 0x0400009C RID: 156
			public ushort Hour;

			// Token: 0x0400009D RID: 157
			public ushort Minute;

			// Token: 0x0400009E RID: 158
			public ushort Second;

			// Token: 0x0400009F RID: 159
			public ushort Millisecond;
		}
	}
}
