﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000018 RID: 24
	public class CurrentConditions
	{
		// Token: 0x17000032 RID: 50
		// (get) Token: 0x060000C5 RID: 197 RVA: 0x0000835A File Offset: 0x0000655A
		// (set) Token: 0x060000C6 RID: 198 RVA: 0x00008362 File Offset: 0x00006562
		[JsonProperty("coord")]
		public Coord Coord { get; set; }

		// Token: 0x17000033 RID: 51
		// (get) Token: 0x060000C7 RID: 199 RVA: 0x0000836B File Offset: 0x0000656B
		// (set) Token: 0x060000C8 RID: 200 RVA: 0x00008373 File Offset: 0x00006573
		[JsonProperty("weather")]
		public List<Weather> Weather { get; set; }

		// Token: 0x17000034 RID: 52
		// (get) Token: 0x060000C9 RID: 201 RVA: 0x0000837C File Offset: 0x0000657C
		// (set) Token: 0x060000CA RID: 202 RVA: 0x00008384 File Offset: 0x00006584
		[JsonProperty("base")]
		public string Base { get; set; }

		// Token: 0x17000035 RID: 53
		// (get) Token: 0x060000CB RID: 203 RVA: 0x0000838D File Offset: 0x0000658D
		// (set) Token: 0x060000CC RID: 204 RVA: 0x00008395 File Offset: 0x00006595
		[JsonProperty("main")]
		public Main Main { get; set; }

		// Token: 0x17000036 RID: 54
		// (get) Token: 0x060000CD RID: 205 RVA: 0x0000839E File Offset: 0x0000659E
		// (set) Token: 0x060000CE RID: 206 RVA: 0x000083A6 File Offset: 0x000065A6
		[JsonProperty("visibility")]
		public long Visibility { get; set; }

		// Token: 0x17000037 RID: 55
		// (get) Token: 0x060000CF RID: 207 RVA: 0x000083AF File Offset: 0x000065AF
		// (set) Token: 0x060000D0 RID: 208 RVA: 0x000083B7 File Offset: 0x000065B7
		[JsonProperty("wind")]
		public Wind Wind { get; set; }

		// Token: 0x17000038 RID: 56
		// (get) Token: 0x060000D1 RID: 209 RVA: 0x000083C0 File Offset: 0x000065C0
		// (set) Token: 0x060000D2 RID: 210 RVA: 0x000083C8 File Offset: 0x000065C8
		[JsonProperty("clouds")]
		public Clouds Clouds { get; set; }

		// Token: 0x17000039 RID: 57
		// (get) Token: 0x060000D3 RID: 211 RVA: 0x000083D1 File Offset: 0x000065D1
		// (set) Token: 0x060000D4 RID: 212 RVA: 0x000083D9 File Offset: 0x000065D9
		[JsonProperty("dt")]
		public long Dt { get; set; }

		// Token: 0x1700003A RID: 58
		// (get) Token: 0x060000D5 RID: 213 RVA: 0x000083E2 File Offset: 0x000065E2
		// (set) Token: 0x060000D6 RID: 214 RVA: 0x000083EA File Offset: 0x000065EA
		[JsonProperty("sys")]
		public Sys Sys { get; set; }

		// Token: 0x1700003B RID: 59
		// (get) Token: 0x060000D7 RID: 215 RVA: 0x000083F3 File Offset: 0x000065F3
		// (set) Token: 0x060000D8 RID: 216 RVA: 0x000083FB File Offset: 0x000065FB
		[JsonProperty("id")]
		public long Id { get; set; }

		// Token: 0x1700003C RID: 60
		// (get) Token: 0x060000D9 RID: 217 RVA: 0x00008404 File Offset: 0x00006604
		// (set) Token: 0x060000DA RID: 218 RVA: 0x0000840C File Offset: 0x0000660C
		[JsonProperty("name")]
		public string Name { get; set; }

		// Token: 0x1700003D RID: 61
		// (get) Token: 0x060000DB RID: 219 RVA: 0x00008415 File Offset: 0x00006615
		// (set) Token: 0x060000DC RID: 220 RVA: 0x0000841D File Offset: 0x0000661D
		[JsonProperty("cod")]
		public long Cod { get; set; }
	}
}
