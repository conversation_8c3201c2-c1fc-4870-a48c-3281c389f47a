using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using SxL.Common;

[assembly: AssemblyVersion("********")]
[assembly: AssemblyTitle("SmartConnector.EBOOPCclient")]
[assembly: AssemblyDescription("OPC client extension to serve data via EWS")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("XEBEC")]
[assembly: AssemblyProduct("SmartConnector.EBOOPCclient")]
[assembly: AssemblyCopyright("All rights reserved. XEBEC Ltd. ©2010")]
[assembly: AssemblyTrademark("")]
[assembly: PublicKey("MIIBKjCB4wYHKoZIzj0CATCB1wIBATAsBgcqhkjOPQEBAiEA/////wAAAAEAAAAAAAAAAAAAAAD///////////////8wWwQg/////wAAAAEAAAAAAAAAAAAAAAD///////////////wEIFrGNdiqOpPns+u9VXaYhrxlHQawzFOw9jvOPD4n0mBLAxUAxJ02CIbnBJNqZnjhE50mt4GffpAEIQNrF9Hy4SxCR/i85uVjpEDydwN9gS3rM6D0oTlF2JjClgIhAP////8AAAAA//////////+85vqtpxeehPO5ysL8YyVRAgEBA0IABKCKBeMs+S8wjjxYYv4EYCxifUGAIK2qzw4rhl4Z13imgCk5gLxlFezdEoyZkeveAjghoh1yA3+XYmR5+UQApKw=")]
[assembly: ComVisible(false)]
[assembly: Guid("1869A5A2-87C3-4FAA-8A9C-7587B10E4E39")]
[assembly: AssemblyFileVersion("********")]
