﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x0200000F RID: 15
	public class City
	{
		// Token: 0x17000012 RID: 18
		// (get) Token: 0x0600007C RID: 124 RVA: 0x0000813A File Offset: 0x0000633A
		// (set) Token: 0x0600007D RID: 125 RVA: 0x00008142 File Offset: 0x00006342
		[JsonProperty("id")]
		public long Id { get; set; }

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x0600007E RID: 126 RVA: 0x0000814B File Offset: 0x0000634B
		// (set) Token: 0x0600007F RID: 127 RVA: 0x00008153 File Offset: 0x00006353
		[JsonProperty("name")]
		public string Name { get; set; }

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x06000080 RID: 128 RVA: 0x0000815C File Offset: 0x0000635C
		// (set) Token: 0x06000081 RID: 129 RVA: 0x00008164 File Offset: 0x00006364
		[JsonProperty("coord")]
		public Coord Coord { get; set; }

		// Token: 0x17000015 RID: 21
		// (get) Token: 0x06000082 RID: 130 RVA: 0x0000816D File Offset: 0x0000636D
		// (set) Token: 0x06000083 RID: 131 RVA: 0x00008175 File Offset: 0x00006375
		[JsonProperty("country")]
		public string Country { get; set; }
	}
}
