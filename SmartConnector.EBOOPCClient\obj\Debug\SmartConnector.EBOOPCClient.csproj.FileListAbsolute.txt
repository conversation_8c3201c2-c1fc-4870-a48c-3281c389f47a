D:\mk\SmartConnector.EBOOPCClient\obj\Debug\SmartConnector.EBOOPCClient.csproj.AssemblyReference.cache
D:\mk\SmartConnector.EBOOPCClient\obj\Debug\SmartConnector.EBOOPCClient.csproj.CoreCompileInputs.cache
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\SmartConnector.EBOOPCClient.dll.config
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\SmartConnector.EBOOPCClient.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\SmartConnector.EBOOPCClient.pdb
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\AMS.Profile.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Common.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Server.Contract.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Common.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Ews.Server.Data.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Ews.Server.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Process.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Newtonsoft.Json.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\OpcDaNetUA.Net4.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\OpcNetBase.Net4.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\SxL.Common.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\StructureMap.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\EntityFramework.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Swashbuckle.Core.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Web.Http.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Security.OAuth.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.AspNet.Identity.Core.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.AspNet.Identity.Owin.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Security.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Owin.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\WebApiThrottle.StrongName.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Net.Http.Formatting.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Cors.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Web.Http.Owin.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Client.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.ValueTuple.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\OpcDaNetUaOption.Net4.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\NLog.dll
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Common.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Server.Contract.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Common.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Ews.Server.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Ews.Server.Data.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Mongoose.Process.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\SxL.Common.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\StructureMap.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\EntityFramework.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Web.Http.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Security.OAuth.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.AspNet.Identity.Core.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.AspNet.Identity.Owin.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Security.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Net.Http.Formatting.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Microsoft.Owin.Cors.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.Web.Http.Owin.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\Ews.Client.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\System.ValueTuple.xml
D:\mk\SmartConnector.EBOOPCClient\bin\Debug\NLog.xml
D:\mk\SmartConnector.EBOOPCClient\obj\Debug\SmartCon.20A5860E.Up2Date
D:\mk\SmartConnector.EBOOPCClient\obj\Debug\SmartConnector.EBOOPCClient.dll
D:\mk\SmartConnector.EBOOPCClient\obj\Debug\SmartConnector.EBOOPCClient.pdb
