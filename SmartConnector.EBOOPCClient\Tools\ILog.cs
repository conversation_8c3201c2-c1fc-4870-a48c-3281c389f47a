﻿using System;
using System.Diagnostics;
using System.IO;
using System.Threading;

namespace SmartConnector.Tools
{
	// Token: 0x02000004 RID: 4
	public class ILog
	{
		// Token: 0x17000002 RID: 2
		// (get) Token: 0x0600000F RID: 15 RVA: 0x0000257D File Offset: 0x0000077D
		// (set) Token: 0x06000010 RID: 16 RVA: 0x00002585 File Offset: 0x00000785
		public ILog.LogLevels Level { get; set; }

		// Token: 0x06000011 RID: 17 RVA: 0x0000258E File Offset: 0x0000078E
		public ILog(string path, ILog.LogLevels logLevel)
		{
			this.Open(path, logLevel);
		}

		// Token: 0x06000012 RID: 18 RVA: 0x000025B5 File Offset: 0x000007B5
		public int Open(string path, ILog.LogLevels logLevel)
		{
			this.Level = logLevel;
			this._Path = path;
			return 0;
		}

		// Token: 0x06000013 RID: 19 RVA: 0x000025C6 File Offset: 0x000007C6
		public void WriteNDisp(ILog.LogLevels level, string message)
		{
			if (this.Write(level, message))
			{
				Console.WriteLine(message);
			}
		}

		// Token: 0x06000014 RID: 20 RVA: 0x000025D8 File Offset: 0x000007D8
		public bool Write(ILog.LogLevels msgLevel, string message)
		{
			if (msgLevel <= this.Level)
			{
				object obj = this._Obj;
				lock (obj)
				{
					try
					{
						string text = Thread.CurrentThread.Name;
						if (string.IsNullOrEmpty(text))
						{
							text = Thread.CurrentThread.ManagedThreadId.ToString();
						}
						using (StreamWriter streamWriter = new StreamWriter(this._Path, true))
						{
							streamWriter.WriteLine(string.Concat(new string[]
							{
								DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
								" [",
								text,
								"] ",
								msgLevel.ToString(),
								", ",
								message
							}));
						}
					}
					catch (Exception ex)
					{
						Trace.WriteLine("Error write to log file: " + ex.Message, "Error");
					}
				}
				return true;
			}
			return false;
		}

		// Token: 0x06000015 RID: 21 RVA: 0x000026F4 File Offset: 0x000008F4
		public bool Write(ILog.LogLevels msgLevel, string message, params object[] args)
		{
			if (msgLevel <= this.Level)
			{
				object obj = this._Obj;
				lock (obj)
				{
					try
					{
						string text = Thread.CurrentThread.Name;
						if (string.IsNullOrEmpty(text))
						{
							text = Thread.CurrentThread.ManagedThreadId.ToString();
						}
						using (StreamWriter streamWriter = new StreamWriter(this._Path, true))
						{
							streamWriter.WriteLine(string.Concat(new string[]
							{
								DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
								" [",
								text,
								"] ",
								msgLevel.ToString(),
								", ",
								string.Format(message, args)
							}));
						}
					}
					catch (Exception ex)
					{
						Trace.WriteLine("Error write to log file: " + ex.Message, "Error");
					}
				}
				return true;
			}
			return false;
		}

		// Token: 0x04000008 RID: 8
		private string _Path = string.Empty;

		// Token: 0x04000009 RID: 9
		private object _Obj = new object();

		// Token: 0x0200001E RID: 30
		public enum LogLevels : byte
		{
			// Token: 0x040000A1 RID: 161
			NONE,
			// Token: 0x040000A2 RID: 162
			ERROR,
			// Token: 0x040000A3 RID: 163
			WARN,
			// Token: 0x040000A4 RID: 164
			INFO,
			// Token: 0x040000A5 RID: 165
			DEBUG,
			// Token: 0x040000A6 RID: 166
			ALL = 9
		}
	}
}
