# License Implementation for SmartConnector EBO OPC Client

## Overview
This document describes the license implementation for the SmartConnector EBO OPC Client application. The licensing system ensures that only authorized users can run the application and provides different license types including time-limited and unlimited licenses.

## Components

### 1. UCMECheckLicense Class (`UCMECheckLicense.cs`)
- **Purpose**: Core license validation functionality
- **Key Methods**:
  - `CheckLicense(out UCMELicInfo licInfo)`: Main license validation method
  - Native DLL import for `CheckLicense.dll`
- **Features**:
  - Validates license through external DLL
  - Supports unlimited licenses (indicated by -1 values)
  - Supports time-limited licenses with expiration dates
  - Handles 2-digit year conversion (adds 2000 if year < 100)
  - Comprehensive error handling and logging

### 2. LicenseManager Class (`LicenseManager.cs`)
- **Purpose**: High-level license management utilities
- **Key Methods**:
  - `IsLicensed()`: Quick check if application is licensed
  - `GetLicenseInfo()`: Retrieve detailed license information
  - `GetLicenseStatusMessage()`: Human-readable license status
  - `GetDaysRemaining()`: Calculate days until license expires
  - `RefreshLicense()`: Force refresh of license information
- **Features**:
  - Caches license information for performance
  - Provides convenient utility methods
  - Thread-safe license checking

### 3. Global Class Updates (`Global.cs`)
- **Added Constants**:
  - `CURRENT_VERSION_NUMBER = 1`: Minimum required license version
  - `DEMO_TIME_IN_MINUTES = 120`: Demo mode time limit
  - `_LicInfo`: Global license information cache

### 4. InitApp Class Updates (`InitApp.cs`)
- **New Method**: `InitializeLicense()`
  - Called during application initialization
  - Validates license before application starts
  - Logs license status and warnings
  - Prevents application startup if license is invalid or expired
- **Integration**: License check integrated into `InitApplication()` method

### 5. Processor Updates
- **EBOOpcClientProcessorBase.cs**:
  - `IsLicensed` property returns `true` (enables licensing)
  - `ValidateCustomLicenseFeatures()` performs comprehensive license validation
  - Provides detailed error and warning messages
  - Integrates with Mongoose framework licensing system

- **CustomEwsServiceHost.cs**:
  - Similar licensing implementation for EWS services
  - Ensures EWS endpoints are also license-protected
  - Consistent error messaging

## License Types

### 1. Unlimited License
- **Indicators**: `dd = -1, mm = -1, yy = -1`
- **Behavior**: Never expires, full functionality
- **Status**: `m_ExpertionUnlimited = true, m_bDemo = false`

### 2. Time-Limited License
- **Indicators**: Valid date values (dd, mm, yy)
- **Behavior**: Expires on specified date
- **Status**: `m_ExpertionUnlimited = false, m_bDemo = false` (if not expired)

### 3. Demo/Expired License
- **Indicators**: License check fails or date has passed
- **Behavior**: Limited or no functionality
- **Status**: `m_bDemo = true`

## CheckLicense.dll

### Sample Implementation (`CheckLicense\CheckLicense.cpp`)
A sample C++ DLL implementation is provided for testing purposes:
- **Function**: `int CheckLicense(int* dd, int* mm, int* yy, int* ver)`
- **Demo Behavior**: Returns a license that expires 30 days from current date
- **Production Note**: Replace with actual license validation logic

### Building the DLL
1. Create a new C++ DLL project in Visual Studio
2. Add `CheckLicense.cpp` and `CheckLicense.def` files
3. Build as a 32-bit or 64-bit DLL (match your application architecture)
4. Place the resulting `CheckLicense.dll` in the application directory

## Integration Points

### Application Startup
1. `InitApp.InitApplication()` calls `InitializeLicense()`
2. License validation occurs before any OPC operations
3. Application terminates if license is invalid

### Runtime Checks
1. Processors validate license through Mongoose framework
2. EWS services validate license before serving requests
3. License information is cached globally for performance

### Error Handling
1. Comprehensive logging of license status
2. User-friendly error messages
3. Graceful degradation for expired licenses

## Configuration

### App.config Settings
No additional configuration required - license checking is automatic.

### Deployment
1. Ensure `CheckLicense.dll` is deployed with the application
2. License file or registry entries (implementation-dependent)
3. Proper permissions for license file access

## Testing

### Test Scenarios
1. **Valid License**: Application starts normally
2. **Expired License**: Application refuses to start with error message
3. **Missing DLL**: Application logs error and refuses to start
4. **Invalid License**: Application logs error and refuses to start
5. **Unlimited License**: Application starts with unlimited access

### Debug Information
- License status logged during application startup
- Detailed error messages for troubleshooting
- License expiration warnings

## Security Considerations

### Current Implementation
- Basic license validation through external DLL
- License information cached in memory
- Standard P/Invoke for DLL communication

### Production Recommendations
1. **Encryption**: Encrypt license files and DLL communication
2. **Obfuscation**: Obfuscate license checking code
3. **Hardware Binding**: Tie licenses to specific hardware
4. **Online Validation**: Periodic online license verification
5. **Tamper Detection**: Detect and respond to license tampering

## Maintenance

### Version Updates
- Update `CURRENT_VERSION_NUMBER` for new releases
- Ensure backward compatibility with existing licenses
- Test license validation with each release

### License Management
- Provide tools for license generation and validation
- Implement license renewal processes
- Monitor license usage and expiration

## Troubleshooting

### Common Issues
1. **DLL Not Found**: Ensure `CheckLicense.dll` is in application directory
2. **Access Denied**: Check file permissions for license files
3. **Date Issues**: Verify system clock is correct
4. **Version Mismatch**: Ensure license version meets minimum requirements

### Logging
All license-related activities are logged with appropriate log levels:
- **INFO**: Successful license validation
- **WARNING**: License expiration warnings
- **ERROR**: License validation failures
