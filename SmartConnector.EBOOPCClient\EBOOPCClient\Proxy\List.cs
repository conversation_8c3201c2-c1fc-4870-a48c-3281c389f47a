﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000011 RID: 17
	public class List
	{
		// Token: 0x17000018 RID: 24
		// (get) Token: 0x0600008A RID: 138 RVA: 0x000081A0 File Offset: 0x000063A0
		// (set) Token: 0x0600008B RID: 139 RVA: 0x000081A8 File Offset: 0x000063A8
		[JsonProperty("dt")]
		public long Dt { get; set; }

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x0600008C RID: 140 RVA: 0x000081B1 File Offset: 0x000063B1
		// (set) Token: 0x0600008D RID: 141 RVA: 0x000081B9 File Offset: 0x000063B9
		[JsonProperty("main")]
		public Main Main { get; set; }

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x0600008E RID: 142 RVA: 0x000081C2 File Offset: 0x000063C2
		// (set) Token: 0x0600008F RID: 143 RVA: 0x000081CA File Offset: 0x000063CA
		[JsonProperty("weather")]
		public List<Weather> Weather { get; set; }

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x06000090 RID: 144 RVA: 0x000081D3 File Offset: 0x000063D3
		// (set) Token: 0x06000091 RID: 145 RVA: 0x000081DB File Offset: 0x000063DB
		[JsonProperty("clouds")]
		public Clouds Clouds { get; set; }

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x06000092 RID: 146 RVA: 0x000081E4 File Offset: 0x000063E4
		// (set) Token: 0x06000093 RID: 147 RVA: 0x000081EC File Offset: 0x000063EC
		[JsonProperty("wind")]
		public Wind Wind { get; set; }

		// Token: 0x1700001D RID: 29
		// (get) Token: 0x06000094 RID: 148 RVA: 0x000081F5 File Offset: 0x000063F5
		// (set) Token: 0x06000095 RID: 149 RVA: 0x000081FD File Offset: 0x000063FD
		[JsonProperty("snow")]
		public Rain Snow { get; set; }

		// Token: 0x1700001E RID: 30
		// (get) Token: 0x06000096 RID: 150 RVA: 0x00008206 File Offset: 0x00006406
		// (set) Token: 0x06000097 RID: 151 RVA: 0x0000820E File Offset: 0x0000640E
		[JsonProperty("sys")]
		public Sys Sys { get; set; }

		// Token: 0x1700001F RID: 31
		// (get) Token: 0x06000098 RID: 152 RVA: 0x00008217 File Offset: 0x00006417
		// (set) Token: 0x06000099 RID: 153 RVA: 0x0000821F File Offset: 0x0000641F
		[JsonProperty("dt_txt")]
		public DateTimeOffset DtTxt { get; set; }

		// Token: 0x17000020 RID: 32
		// (get) Token: 0x0600009A RID: 154 RVA: 0x00008228 File Offset: 0x00006428
		// (set) Token: 0x0600009B RID: 155 RVA: 0x00008230 File Offset: 0x00006430
		[JsonProperty("rain", NullValueHandling = NullValueHandling.Ignore)]
		public Rain Rain { get; set; }
	}
}
