﻿using System;

namespace SmartConnector.Tools
{
	// Token: 0x02000002 RID: 2
	public static class ArrayHelper
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
		public static int ClearArray(Array arr, byte value)
		{
			if (value == 0)
			{
				Array.Clear(arr, 0, arr.Length);
				return 0;
			}
			switch (arr.Rank)
			{
			case 1:
				Array.Clear(arr, 0, arr.Length);
				break;
			case 2:
			{
				int num = arr.GetUpperBound(0) + 1;
				int num2 = arr.GetUpperBound(1) + 1;
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						arr.SetValue(value, i, j);
					}
				}
				break;
			}
			case 3:
			{
				int num = arr.GetUpperBound(0) + 1;
				int num2 = arr.GetUpperBound(1) + 1;
				int num3 = arr.GetUpperBound(2) + 1;
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						for (int k = 0; k < num3; k++)
						{
							arr.SetValue(value, i, j, k);
						}
					}
				}
				break;
			}
			}
			return 0;
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002138 File Offset: 0x00000338
		public static int ClearArrayByDimension(Array arr, int D1Index, byte value)
		{
			switch (arr.Rank)
			{
			case 1:
				Array.Clear(arr, 0, arr.Length);
				break;
			case 2:
			{
				int num = arr.GetUpperBound(1) + 1;
				for (int i = 0; i < num; i++)
				{
					arr.SetValue(value, D1Index, i);
				}
				break;
			}
			case 3:
			{
				int num = arr.GetUpperBound(1) + 1;
				int num2 = arr.GetUpperBound(2) + 1;
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						arr.SetValue(value, D1Index, i, j);
					}
				}
				break;
			}
			}
			return 0;
		}

		// Token: 0x06000003 RID: 3 RVA: 0x000021D8 File Offset: 0x000003D8
		public static int CompArrayDimension(Array arr, int D1IndexSrc, int D1IndexDst)
		{
			switch (arr.Rank)
			{
			case 1:
				return -1;
			case 2:
			{
				int num = arr.GetUpperBound(1) + 1;
				for (int i = 0; i < num; i++)
				{
					if ((byte)arr.GetValue(D1IndexSrc, i) != (byte)arr.GetValue(D1IndexDst, i))
					{
						return 1;
					}
				}
				break;
			}
			case 3:
			{
				int num = arr.GetUpperBound(1) + 1;
				int num2 = arr.GetUpperBound(2) + 1;
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						if ((byte)arr.GetValue(D1IndexSrc, i, j) != (byte)arr.GetValue(D1IndexDst, i, j))
						{
							return 1;
						}
					}
				}
				break;
			}
			}
			return 0;
		}

		// Token: 0x06000004 RID: 4 RVA: 0x0000228C File Offset: 0x0000048C
		public static int CompArrayDimension(Array arr, int D1IndexSrc, int D1IndexDst, int D2IndexSrc, int D2IndexDst)
		{
			switch (arr.Rank)
			{
			case 1:
				return -1;
			case 2:
				return -1;
			case 3:
			{
				arr.GetUpperBound(1);
				int num = arr.GetUpperBound(2) + 1;
				for (int i = 0; i < num; i++)
				{
					if ((byte)arr.GetValue(D1IndexSrc, D2IndexSrc, i) != (byte)arr.GetValue(D1IndexDst, D2IndexDst, i))
					{
						return 1;
					}
				}
				break;
			}
			}
			return 0;
		}

		// Token: 0x06000005 RID: 5 RVA: 0x000022FC File Offset: 0x000004FC
		public static int CopyArrayDimension(Array arr, int D1IndexSrc, int D1IndexDst)
		{
			switch (arr.Rank)
			{
			case 1:
				return -1;
			case 2:
			{
				int num = arr.GetUpperBound(1) + 1;
				Array.Copy(arr, D1IndexSrc, arr, D1IndexDst * num, num);
				break;
			}
			case 3:
			{
				int num = arr.GetUpperBound(1) + 1;
				int num2 = arr.GetUpperBound(2) + 1;
				Array.Copy(arr, D1IndexSrc, arr, D1IndexDst * num * num2, num * num2);
				break;
			}
			}
			return 0;
		}

		// Token: 0x06000006 RID: 6 RVA: 0x00002368 File Offset: 0x00000568
		public static int CopySingleDimToMultiDimArray(Array src, Array dst, int D1Index)
		{
			switch (dst.Rank)
			{
			case 1:
				Array.Copy(src, 0, dst, D1Index, dst.Length);
				break;
			case 2:
			{
				int num = dst.GetUpperBound(1) + 1;
				for (int i = 0; i < num; i++)
				{
					if (i >= src.Length)
					{
						break;
					}
					dst.SetValue(src.GetValue(i), D1Index, i);
				}
				break;
			}
			case 3:
			{
				int num = dst.GetUpperBound(1) + 1;
				int num2 = dst.GetUpperBound(2) + 1;
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						int num3 = i * num2 + j;
						if (num3 >= src.Length)
						{
							break;
						}
						dst.SetValue(src.GetValue(num3), D1Index, i, j);
					}
				}
				break;
			}
			}
			return 0;
		}
	}
}
