﻿using System;
using Ews.Server.Contract.Processor;
using Mongoose.Ews.Server;

namespace SmartConnector.EBOOPCClient.EwsServer
{
	// Token: 0x02000019 RID: 25
	public class CustomDataExchange : MongooseDataExchange
	{
		// Token: 0x060000DE RID: 222 RVA: 0x00008426 File Offset: 0x00006626
		protected override SetValuesProcessor CreateSetValuesProcessor()
		{
			return new CustomSetValuesProcessor();
		}
	}
}
