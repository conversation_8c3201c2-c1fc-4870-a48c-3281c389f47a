﻿using System;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using AMS.Profile;
using Mongoose.Common;
using SmartConnector.Tools;
using SxL.Common;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x02000006 RID: 6
	public static class InitApp
	{
		// Token: 0x06000017 RID: 23 RVA: 0x0000282C File Offset: 0x00000A2C
		public static int InitApplication()
		{
			int num = 0;
			bool flag = false;
			Logger.LogInfo(LogCategory.Processor, new object[]
			{
				"EBO OPC Client - InitApplication"
			});
			try
			{
				string name = "LogAppend";
				if (ConfigurationManager.AppSettings.Get(name) != null)
				{
					flag = (int.Parse(ConfigurationManager.AppSettings.Get(name)) == 1);
				}
			}
			catch (Exception)
			{
			}
			InitApp._logFileName = Path.Combine(ServerHelper._OPCClientLogFolder, "EBOOPCclient.log");
			if (!flag)
			{
				File.Delete(InitApp._logFileName);
			}
			InitApp._ILog = new ILog(InitApp._logFileName, ILog.LogLevels.ALL);
			string fileVersion = FileVersionInfo.GetVersionInfo(Assembly.GetExecutingAssembly().Location).FileVersion;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "===================== EBO OPC Client, version: {0} =====================", new object[]
			{
				fileVersion
			});
			try
			{
				string text = "LogFilter";
				ILog.LogLevels level = ILog.LogLevels.ALL;
				if (ConfigurationManager.AppSettings.Get(text) != null)
				{
					text = "LogFilter";
					string text2 = ConfigurationManager.AppSettings.Get(text);
					InitApp._ILog.Write(ILog.LogLevels.INFO, "{0} = '{1}'", new object[]
					{
						text,
						text2
					});
					level = (ILog.LogLevels)int.Parse(text2);
					text = "CertificateDomainMustMatch";
					text2 = ConfigurationManager.AppSettings.Get(text);
					if (!string.IsNullOrEmpty(text2))
					{
						InitApp._CertificateDomainMustMatch = (int.Parse(text2) == 1);
					}
					InitApp._ILog.Write(ILog.LogLevels.INFO, "{0} = '{1}'", new object[]
					{
						text,
						InitApp._CertificateDomainMustMatch
					});
					text = "UaAccessWithoutSecurityPreferred";
					text2 = ConfigurationManager.AppSettings.Get(text);
					if (!string.IsNullOrEmpty(text2))
					{
						InitApp._UaAccessWithoutSecurityPreferred = (int.Parse(text2) == 1);
					}
					InitApp._ILog.Write(ILog.LogLevels.INFO, "{0} = '{1}'", new object[]
					{
						text,
						InitApp._UaAccessWithoutSecurityPreferred
					});
				}
				else
				{
					InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitApplication, no app settings section found");
				}
				InitApp._ILog.Level = level;
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitApplication, app settings error '{0}'", new object[]
				{
					ex.Message
				});
			}
			ServerHelper._lstOpcClientManager.Clear();
			FileInfo[] files = new DirectoryInfo(ServerHelper._OPCClientFolder).GetFiles("OPC*.ini");
			if (files.Length == 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitApplication, no OPC servers INI files found, can't continue");
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "InitApplication, # of OPC servers INI files: {0}", new object[]
			{
				files.Length
			});
			FileInfo[] array = files;
			for (int i = 0; i < array.Length; i++)
			{
				if ((num = InitApp.InitOPCServer(array[i].FullName)) != 0)
				{
					return num;
				}
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "InitApplication, EBO OPC Client ended (rc = {0})", new object[]
			{
				num
			});
			return num;
		}

		// Token: 0x06000018 RID: 24 RVA: 0x00002B1C File Offset: 0x00000D1C
		public static int InitOPCServer(string OPCServerINIFileName)
		{
			InitApp._ILog.Write(ILog.LogLevels.INFO, "InitOPCServer, ini file: '{0}'", new object[]
			{
				OPCServerINIFileName
			});
			int count = ServerHelper._lstOpcClientManager.Count;
			OpcClientManager opcClientManager = new OpcClientManager
			{
				_OPCServerIndex = count
			};
			int result;
			if ((result = InitApp.ReadOPCServerSettings(OPCServerINIFileName, ref opcClientManager)) != 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitOPCServer '{0}', ReadOPCServerSettings fail, can't continue", new object[]
				{
					OPCServerINIFileName
				});
				return result;
			}
			if ((result = opcClientManager.Init()) != 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitOPCServer '{0}', opcClientManager.Init fail, can't continue", new object[]
				{
					OPCServerINIFileName
				});
				return result;
			}
			FileInfo[] files = new DirectoryInfo(ServerHelper._OPCClientFolder).GetFiles("OPC-Server_" + opcClientManager._OPCLogicalName + "#*.txt");
			if (files.Length == 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitOPCServer '{0}' fail, no OPC servers Groups files found, can't continue", new object[]
				{
					OPCServerINIFileName
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "InitOPCServer '{0}', # of OPC Groups files: {1}", new object[]
			{
				OPCServerINIFileName,
				files.Length
			});
			foreach (FileInfo fileInfo in files)
			{
				if ((result = InitApp.AddOPCServerGroupsItems(fileInfo.FullName, ref opcClientManager)) != 0)
				{
					InitApp._ILog.Write(ILog.LogLevels.ERROR, "InitOPCServer '{0}', AddOPCServerGroupsItems '{1}' fail", new object[]
					{
						OPCServerINIFileName,
						fileInfo.FullName
					});
					return result;
				}
			}
			ServerHelper._lstOpcClientManager.Add(opcClientManager);
			InitApp._ILog.Write(ILog.LogLevels.INFO, "InitOPCServer '{0}' done, # of OPC servers: {1}", new object[]
			{
				OPCServerINIFileName,
				ServerHelper._lstOpcClientManager.Count
			});
			return result;
		}

		// Token: 0x06000019 RID: 25 RVA: 0x00002CAC File Offset: 0x00000EAC
		private static int ReadOPCServerSettings(string OPCServerINIFileName, ref OpcClientManager opcClientManager)
		{
			int num = 0;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, file: '{0}'", new object[]
			{
				OPCServerINIFileName
			});
			Ini ini = new Ini(OPCServerINIFileName);
			string text = "OPC";
			if (ini.HasSection(text))
			{
				string text2 = "Node";
				if (ini.HasEntry(text, text2))
				{
					string text3 = ini.GetValue(text, text2).ToString();
					opcClientManager._OPCNode = text3;
					InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
					{
						text2,
						text3
					});
				}
				text2 = "Server";
				if (ini.HasEntry(text, text2))
				{
					string text3 = ini.GetValue(text, text2).ToString();
					opcClientManager._OPCServerName[0] = text3;
					InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
					{
						text2,
						text3
					});
					text2 = "RedundantServer";
					if (ini.HasEntry(text, text2))
					{
						text3 = ini.GetValue(text, text2).ToString();
						opcClientManager._OPCServerName[1] = text3;
						InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
						{
							text2,
							text3
						});
					}
					text2 = "LogicalName";
					if (ini.HasEntry(text, text2))
					{
						text3 = ini.GetValue(text, text2).ToString();
						opcClientManager._OPCLogicalName = text3;
						InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
						{
							text2,
							text3
						});
						text2 = "ClsID";
						if (ini.HasEntry(text, text2))
						{
							text3 = ini.GetValue(text, text2).ToString();
							opcClientManager._OPCClassID = text3;
							InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
							{
								text2,
								text3
							});
							text2 = "uaUser";
							if (ini.HasEntry(text, text2))
							{
								text3 = ini.GetValue(text, text2).ToString();
								opcClientManager._OPCUAUsername = text3;
								InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
								{
									text2,
									text3
								});
							}
							text2 = "uaPassword";
							if (ini.HasEntry(text, text2))
							{
								text3 = ini.GetValue(text, text2).ToString();
								opcClientManager._OPCUAPassword = text3;
								InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
								{
									text2,
									text3
								});
							}
							text2 = "GroupRate";
							if (ini.HasEntry(text, text2))
							{
								text3 = ini.GetValue(text, text2).ToString();
								opcClientManager._OPCGroupRate = int.Parse(text3);
								InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
								{
									text2,
									text3
								});
								text2 = "InitTimeout";
								if (ini.HasEntry(text, text2))
								{
									text3 = ini.GetValue(text, text2).ToString();
									opcClientManager._OPCInitTimeout = int.Parse(text3);
									InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
									{
										text2,
										text3
									});
									text2 = "LogMessageFilter";
									if (ini.HasEntry(text, text2))
									{
										text3 = ini.GetValue(text, text2).ToString();
										opcClientManager._logFilter = (ILog.LogLevels)int.Parse(text3);
										InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
										{
											text2,
											text3
										});
										text2 = "CheckLastUpdateTime";
										if (ini.HasEntry(text, text2))
										{
											text3 = ini.GetValue(text, text2).ToString();
											opcClientManager._OPCheckLastUpdateTime = (int.Parse(text3) != 0);
											InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
											{
												text2,
												text3
											});
											text2 = "ForceReadAfterAddItem";
											if (ini.HasEntry(text, text2))
											{
												text3 = ini.GetValue(text, text2).ToString();
												opcClientManager._OPCForceReadAfterAddItem = (int.Parse(text3) != 0);
												InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
												{
													text2,
													text3
												});
												text2 = "AddItemsCount";
												if (ini.HasEntry(text, text2))
												{
													text3 = ini.GetValue(text, text2).ToString();
													opcClientManager._OPCAddItemsCount = (int.Parse(text3) != 0);
													InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, {0}: '{1}'", new object[]
													{
														text2,
														text3
													});
												}
												else
												{
													num = -1;
													InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
													{
														OPCServerINIFileName,
														text2
													});
												}
											}
											else
											{
												num = -1;
												InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
												{
													OPCServerINIFileName,
													text2
												});
											}
										}
										else
										{
											num = -1;
											InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
											{
												OPCServerINIFileName,
												text2
											});
										}
									}
									else
									{
										num = -1;
										InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
										{
											OPCServerINIFileName,
											text2
										});
									}
								}
								else
								{
									num = -1;
									InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
									{
										OPCServerINIFileName,
										text2
									});
								}
							}
							else
							{
								num = -1;
								InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
								{
									OPCServerINIFileName,
									text2
								});
							}
						}
						else
						{
							num = -1;
							InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
							{
								OPCServerINIFileName,
								text2
							});
						}
					}
					else
					{
						num = -1;
						InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
						{
							OPCServerINIFileName,
							text2
						});
					}
				}
				else
				{
					num = -1;
					InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing entry '{1}'", new object[]
					{
						OPCServerINIFileName,
						text2
					});
				}
			}
			else
			{
				num = -1;
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "ReadOPCServerSettings, invalid ini file format: '{0}', missing section '{1}'", new object[]
				{
					OPCServerINIFileName,
					text
				});
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "ReadOPCServerSettings, file: '{0}', rc = {1}", new object[]
			{
				OPCServerINIFileName,
				num
			});
			return num;
		}

		// Token: 0x0600001A RID: 26 RVA: 0x00003284 File Offset: 0x00001484
		private static int AddOPCServerGroupsItems(string GroupItemsFileName, ref OpcClientManager opcClientManager)
		{
			int result = 0;
			int i = 0;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "AddOPCServerGroupsItems, file: '{0}'", new object[]
			{
				GroupItemsFileName
			});
			string text = File.ReadAllText(GroupItemsFileName, Encoding.UTF8);
			if (string.IsNullOrEmpty(text))
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}' ReadAllText() fail", new object[]
				{
					GroupItemsFileName
				});
				return -1;
			}
			int num = text.IndexOf('\r');
			num++;
			string text2 = text.Substring(0, num).Trim();
			if (text2.Substring(0, 3) != "V1.")
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}' version line not found", new object[]
				{
					GroupItemsFileName
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "AddOPCServerGroupsItems, file: '{0}' version '{1}", new object[]
			{
				GroupItemsFileName,
				text2
			});
			text = text.Substring(num);
			int num2 = GroupItemsFileName.IndexOf('#');
			string text3 = GroupItemsFileName.Substring(num2 + 1);
			text3 = text3.Substring(0, text3.Length - 4);
			char[] separator = new char[]
			{
				'\r'
			};
			string[] array = text.Split(separator, StringSplitOptions.RemoveEmptyEntries);
			if (array.Length < 3)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}' invalid format!", new object[]
				{
					GroupItemsFileName
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "AddOPCServerGroupsItems, file: '{0}' # of lines: {1}", new object[]
			{
				GroupItemsFileName,
				array.Length
			});
			try
			{
				for (i = 0; i < array.Length; i += 3)
				{
					char[] trimChars = new char[]
					{
						' ',
						'\r',
						'\n',
						'\t'
					};
					string text4 = array[i].Trim(trimChars);
					if (!string.IsNullOrEmpty(text4))
					{
						string[] separator2 = new string[]
						{
							"<$>"
						};
						string[] array2 = text4.Split(separator2, StringSplitOptions.RemoveEmptyEntries);
						string text5 = array2[0];
						if (opcClientManager._mapItemIDToOpcItem.ContainsKey(text5))
						{
							InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}', error on line: {1}, item '{2}' already exist!", new object[]
							{
								GroupItemsFileName,
								i + 1,
								text5
							});
						}
						else
						{
							string text6 = array2[0];
							if (array2.Length == 2)
							{
								text6 = array2[1];
							}
							if (opcClientManager._mapEWSItemIDToOpcItem.ContainsKey(text6))
							{
								InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}', error on line: {1}, item '{2}' already exist!", new object[]
								{
									GroupItemsFileName,
									i + 1,
									text6
								});
							}
							else
							{
								string text7 = "string";
								string text8 = array[i + 2].Trim();
								int num3 = 1;
								char[] separator3 = new char[]
								{
									'['
								};
								string[] array3 = text8.Split(separator3, StringSplitOptions.RemoveEmptyEntries);
								if (array3.Length == 2)
								{
									text8 = array3[0].Trim();
									string s = array3[1].Remove(array3[1].Length - 1);
									try
									{
										num3 = int.Parse(s);
									}
									catch (Exception)
									{
									}
									InitApp._ILog.Write(ILog.LogLevels.INFO, "AddOPCServerGroupsItems, file: '{0}' array item: {1}, # of items: {2}", new object[]
									{
										GroupItemsFileName,
										text6,
										num3
									});
								}
								if (!InitApp.ConvertOpcTypeToCommonType(text8, out text7))
								{
									InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}', error on line: {1}, item '{2}', type '{3}' not define!", new object[]
									{
										GroupItemsFileName,
										i + 1,
										text6,
										text8
									});
								}
								else
								{
									InitApp._ILog.Write(ILog.LogLevels.DEBUG, "AddOPCServerGroupsItems, file: '{0}', line: {1}, item '{2}', type '{3}' => '{4}'", new object[]
									{
										GroupItemsFileName,
										i + 1,
										text6,
										text8,
										text7
									});
									string itemPath = array[i + 1].Trim();
									int count = opcClientManager._lstOPCItems.Count;
									int address = count;
									EBOOpcItem eboopcItem = new EBOOpcItem(text5, itemPath, true, count, VarEnum.VT_EMPTY, address, text3, text6);
									eboopcItem._DataTypeFromUCME = text7;
									eboopcItem._NumOfItemsInArray = num3;
									opcClientManager._lstOPCItems.Add(eboopcItem);
									opcClientManager._mapItemIDToOpcItem.Add(text5, eboopcItem);
									opcClientManager._mapEWSItemIDToOpcItem.Add(text6, eboopcItem);
								}
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddOPCServerGroupsItems, file: '{0}', error on line: {1}, '{2}'", new object[]
				{
					GroupItemsFileName,
					i + 1,
					ex.Message
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "AddOPCServerGroupsItems, file: '{0}', # of items: {1}", new object[]
			{
				GroupItemsFileName,
				opcClientManager._lstOPCItems.Count
			});
			return result;
		}

		// Token: 0x0600001B RID: 27 RVA: 0x000036F4 File Offset: 0x000018F4
		public static int StartApplicationSetLogFile()
		{
			Logger.LogInfo(LogCategory.Processor, new object[]
			{
				"EBO OPC Client - StartApplication"
			});
			InitApp._logFileName = Path.Combine(ServerHelper._OPCClientLogFolder, "EBOOPCclient.log");
			InitApp._ILog = new ILog(InitApp._logFileName, ILog.LogLevels.ALL);
			string fileVersion = FileVersionInfo.GetVersionInfo(Assembly.GetExecutingAssembly().Location).FileVersion;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "===================== EBO OPC Client, version: {0} =====================", new object[]
			{
				fileVersion
			});
			InitApp._ILog.Write(ILog.LogLevels.INFO, "StartApplication, # of OPC servers: {0}", new object[]
			{
				ServerHelper._lstOpcClientManager.Count
			});
			return 0;
		}

		// Token: 0x0600001C RID: 28 RVA: 0x0000379C File Offset: 0x0000199C
		public static int StartApplication()
		{
			int num = 0;
			int num2 = 0;
			while (num2 < ServerHelper._lstOpcClientManager.Count && (num = ServerHelper._lstOpcClientManager[num2].Start()) == 0)
			{
				num2++;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "StartApplication end, rc = {0}", new object[]
			{
				num
			});
			return num;
		}

		// Token: 0x0600001D RID: 29 RVA: 0x000037F8 File Offset: 0x000019F8
		public static bool ConvertOpcTypeToCommonType(string strOpcDataType, out string strDataType)
		{
			strDataType = "string";
			for (int i = 0; i < InitApp._UATypes.Length; i++)
			{
				if (string.Compare(strOpcDataType, InitApp._UATypes[i].m_strKey, true) == 0)
				{
					strDataType = InitApp._UATypes[i].m_strVal;
					return true;
				}
			}
			for (int i = 0; i < InitApp._DATypes.Length; i++)
			{
				if (string.Compare(strOpcDataType, InitApp._DATypes[i].m_strKey, true) == 0)
				{
					strDataType = InitApp._DATypes[i].m_strVal;
					return true;
				}
			}
			return true;
		}

		// Token: 0x0400000D RID: 13
		private static string _logFileName = string.Empty;

		// Token: 0x0400000E RID: 14
		public static ILog _ILog = null;

		// Token: 0x0400000F RID: 15
		public static bool _CertificateDomainMustMatch = true;

		// Token: 0x04000010 RID: 16
		public static bool _UaAccessWithoutSecurityPreferred = true;

		// Token: 0x04000011 RID: 17
		private static KeyVal[] _UATypes = new KeyVal[]
		{
			new KeyVal("Boolean", "bool"),
			new KeyVal("SByte", "char"),
			new KeyVal("Byte", "byte"),
			new KeyVal("Int16", "I16"),
			new KeyVal("UInt16", "UI16"),
			new KeyVal("Int32", "I32"),
			new KeyVal("UInt32", "UI32"),
			new KeyVal("StatusCode", "UI32"),
			new KeyVal("Int64", "I64"),
			new KeyVal("UInt64", "UI64"),
			new KeyVal("DateTime", "datetime"),
			new KeyVal("Float", "float"),
			new KeyVal("Double", "double"),
			new KeyVal("String", "string"),
			new KeyVal("ByteString", "string"),
			new KeyVal("XmlElement", "string")
		};

		// Token: 0x04000012 RID: 18
		private static KeyVal[] _DATypes = new KeyVal[]
		{
			new KeyVal("BOOL", "bool"),
			new KeyVal("I1", "char"),
			new KeyVal("UI1", "byte"),
			new KeyVal("I2", "I16"),
			new KeyVal("UI2", "UI16"),
			new KeyVal("I4", "I32"),
			new KeyVal("UI4", "UI32"),
			new KeyVal("R4", "float"),
			new KeyVal("R8", "double"),
			new KeyVal("BSTR", "string"),
			new KeyVal("Date", "datetime")
		};
	}
}
