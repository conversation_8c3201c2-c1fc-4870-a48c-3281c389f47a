<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Mongoose.Ews.Server.Data</name>
    </assembly>
    <members>
        <member name="P:Mongoose.Ews.Server.Data.ClaimsTransformer.AuthenticationToken">
            <summary>
            The name of the Claim that will be put on the authenticated ClaimsPrincipal which contains our authentication token
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.ClaimsTransformer.AuthenticationTokenIv">
            <summary>
            The name of the intialization vector used to encypt the AuthenticationToken
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.AlternateId">
            <summary>
            AlternateId is what will be exposed to consumers and not our internal persistance Id.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.Source">
            <summary>
            The Parent for the AlarmEvent
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.Acknowledgeable">
            <summary>
            Indicates if this particular AlarrmEvent is acknowledgabe or not.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.OccurrenceDateTime">
            <summary>
            The DateTime where the parent EwsAlarmItem first went into Alarm.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.TransitionDateTime">
            <summary>
            The DateTime when the transition to the state represented by this EwsAlarmEvent occurred.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEvent.Type">
            <summary>
            The Type of this AlarmEvent
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsAlarmEventType">
            <summary>
            Models an EWS AlarmEventType for a given EwsServer
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEventType.Name">
            <summary>
            The actual name of the EwsAlarmEventType
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmEventType.Description">
            <summary>
            Description of this HistoryItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmItem.TransitionModel">
            <summary>
            The transition model for the AlarmItem.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsAlarmItem.ValueItem">
            <summary>
            ValueItem this is logging Alarms for (Optional)
            </summary>
            <remarks>
            This is NOT required.
            </remarks>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsContainerItem">
            <summary>
            An EWS ContainerItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsCommonDataItem`1">
            <summary>
            Any item which represents a Common Data Model item: ContainerItem, ValueItem, HistoryItem, AlarmItem or Enum.
            </summary>
            <remarks>
            Because we don't require Parent, we have a deleting issue where children will not be automatically deleted by EF.  
            I've tried to configure EF to handle this automatically, but I was unsuccessful (damn ORMs!).  As such, if you create a sub-class of this you need to update the 
            MongooseDbContext (DeleteContainerItem) to properly handle deleting children manually.
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsCommonDataItem`1.AlternateId">
            <summary>
            AlternateId is what will be exposed to consumers and not our internal persistance Id.
            </summary>
            <remarks>
            When we added a non-clustered index to this column, we opted to shorten it's allowed length to 450 characters so the index would work.  If the becomes a problem, then we need to re-consider
            this approach.  For now, we'll just go with it.
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsCommonDataItem`1.Parent">
            <summary>
            The Parent ContainerItem of this item.  A NULL value indicates that this item is parented by the root ContainerItem of the Server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsCommonDataItem`1.ParentChangedAt">
            <summary>
            The DateTime in UTC when the Parent was changed
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsCommonDataItem`1.DataModelType">
            <summary>
            The enumeration type for this item
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsHierarchyChanged">
            <summary>
            Represents details on any EwsCommonDataItem whos hierarchy has changed.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHierarchyChanged.ItemType">
            <summary>
            The type of AlternateId.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsHistoryItem">
            <summary>
            Defines the fact that history is tracked for a ValueItem.
            </summary>
            <remarks>
            We could do many things here but for now, all History is CoV based and managed by the Adapter.
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.Name">
            <summary>
            Name of this HistoryItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.Description">
            <summary>
            Description of this HistoryItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.Type">
            <summary>
            Associated ValueItem's value at creation
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.Unit">
            <summary>
            Associated ValueItem's value at creation
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.IsActive">
            <summary>
            Is actively logging HistoryRecords entries
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryItem.ValueItem">
            <summary>
            ValueItem this is logging History for.
            </summary>
            <remarks>
            This is required even though we don't define it as as such because EF can't handle inverse 1:many relationships and I don't want to model an IList in the ApiUser
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsHistoryRecord.EwsHistoryItem">
            <summary>
            The Parent for the HistoryRecord
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsNotification">
            <summary>
            Represents a session to report subscribed events to.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotification.SubscriptionId">
            <summary>
            Id of the Subscription this is a notification session for.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotification.Subscription">
            <summary>
            Subscription this is a notification session for.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotification.AlternateId">
            <summary>
            Unique identifier for the notification session.  This is auto-generated and should never be changed.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotification.CreatedOn">
            <summary>
            Date in UTC when the notification session was created.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsNotificationItem">
            <summary>
            Represents a single notification of an subscribed event to a caller within the Notification session.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotificationItem.NotificationId">
            <summary>
            Id of the Notification this item is part of.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotificationItem.Notification">
            <summary>
            Notification this item is part of.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotificationItem.ValueChanged">
            <summary>
            When the Notification.Subscription is for Value changes, this will represent the notification data for the Notification session.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotificationItem.HierarchyChanged">
            <summary>
            When the Notification.Subscription is for Hierarchy changes, this will represent the notification data for the Notification session.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsNotificationItem.AlarmChanged">
            <summary>
            When the Notification.Subscription is for Alarm changes, this will represent a single AlarmEvent to be returned.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServer">
            <summary>
            Represents a logical instance of an EWS Serve endpoint.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.Name">
            <summary>
            A logical name to give this EWS Server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.Address">
            <summary>
            The address of the endoint of this EWSServer.  http://localhost:57621/DataExchange for example.  Addresses must be unique within the context of Mongoose.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.Realm">
            <summary>
            The Realm value for the Digest Authentication
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.AllowCookies">
            <summary>
            If true, cookie based authentication will be allowed.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.MaxRecievedMessageSize">
            <summary>
            The maximum recieved message size in bytes that the EWS server can receive before the request is rejected
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.IsLicensed">
            <summary>
            Indicates that the ServiceHost requires a license.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.AssemblyFile">
            <summary>
            The relative or absolute path to the assembly DLL that contains the EwsServerHost sub-classes.
            If NULL, then the default is assumed to be "Mongoose.Ews.Server.dll".
            </summary>
            <remarks>
            If relative pathing is used, then the assembly must be co-located with the runtime service binaries.
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.ClassName">
            <summary>
            The fully namspaced class name eg Mongoose.Ews.Server.MongooseEwsServiceHost of the endpoint to use.
            If NULL, then the default is assumed to be "Mongoose.Ews.Server.MongooseEwsServiceHost".
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.IsAutoStart">
            <summary>
            Indicates that this Server will automatically start when Mongoose starts up.  This has no meaning at any other time.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.IsRunning">
            <summary>
            Indicates that this Server is currently running.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.RootContainerItemAlternateId">
            <summary>
            Since we don't persist the "root" ContainerItem, this is the AlternateId we will give it when we server it up.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.RootContainerItemDescription">
            <summary>
            Since we don't persist the "root" ContainerItem, this is the Description we will give it when we server it up.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.SupportedMethods">
            <summary>
            Enumerates the methods supported by this EWS Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServer.SupportedHashingAlgorithms">
            <summary>
            Enumerates the HTTP Digest Authentication hashing algorithms supported by this EWS Server
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServerDataAdapter">
            <summary>
            Adapter to read and write data to the database for native EWS Servers
            </summary>
            <remarks>
            This adapter is tightly coupled to a MongooseDbContext's interfaces via dependency injection.  As such, it should be a "short lived" instance however you can call CreateDatabaseContext to refresh your connection.
            To consume, you can optionally ConnectExisting to authenticate yourself against an existing EwsServer or ConnectNew to create a brand new EwsServer instance.  In the latter case you are 
            immediately authenticated.
            </remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AutoCommit">
            <summary>
            If true, supporting methods will automatically commit changes to the data store.  Not all methods which add, delete, or modify data support deferred commit.  
            In general most Modify and Delete methods do support delayed commit (Server, User, and AlternateId changes do NOT).
            
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.HasPendingChanges">
            <summary>
            Returns true if there are uncommitted changes pending.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.CommitChanges">
            <summary>
            Commits all pending changes to the database.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DiscardChanges">
            <summary>
            Discards any pending changes and recreates the underlying DB context references.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ConnectExisting(System.String,System.String,System.String)">
            <summary>
            Instantiates an EwsServerDataAdapter session by authenticating to a pre-existing EwsServer instance.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ConnectExisting(System.String,System.String)">
            <summary>
            Instantiates an EwsServerDataAdapter from an encrypted security token.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ConnectNew(System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            Instantiates an EwsServerDataAdapter session by creating a brand new Server instance.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.UsedNameSpace">
            <summary>
            Namespace used in EWS messages
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ServerName">
            <summary>
            Name of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ServerId">
            <summary>
            Id of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ServerVersion">
            <summary>
            The EWS specification version which this server supports.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SupportedMethodNames">
            <summary>
            Returns the supported methods.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SupportedProfiles">
            <summary>
            Returns the profiles supported.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SupportedAlarmEventTypes">
            <summary>
            Returns the AlarmEventTypes supported.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SupportedSystemEventTypes">
            <summary>
            Returns the SystemEventTypes supported.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.MaximumSubscriptionDurationInMinutes">
            <summary>
            Server specific maximum subscription duration.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.MetadataAllowed">
            <summary>
            Indicates whether metadata should be allowed or not.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DefaultSubscriptionDuration">
            <summary>
            Returns default subscription duration.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.PageSize">
            <summary>
            Returns the logical page size implemented by the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.LogCategory">
            <summary>
            Returns the category which data will be loagged.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.IsLicensed">
            <summary>
            Returns whether the Server is licensed or not.
            </summary>
            <remarks>This value is retrieved from the database however enforcement is determined from RequiresLicense method</remarks>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.CustomLicenseFeatures">
            <summary>
            Returns the Custom licensing features (if any) used by this server's host license.
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DefaultHostAssemblyFile">
            <summary>
            Default assembly for the EwsServerHost class.
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DefaultHostClass">
            <summary>
            Default name for the EwsServerHost class.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.CreateDatabaseContext">
            <summary>
            Will regenerate the database contexts this adapter is connected to.  You only need to call this if you know that objects may have been modified by another process. 
            All pending changes will be lost.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.Server">
            <summary>
            The Server which is currently connected to the Adapter.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AuthenticatedUser">
            <summary>
            The user that authenticated to the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.IsConnected">
            <summary>
            Returns true if we're currently connected to an server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ServerDb">
            <summary>
            EwsServer data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.UserDb">
            <summary>
            EwsUser data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ContainerItemDb">
            <summary>
            EwsContainerItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ValueItemDb">
            <summary>
            EwsValueItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.HistoryItemDb">
            <summary>
            EwsHistoryItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.HistoryRecordDb">
            <summary>
            EwsHistoryRecord data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ServerRequestDb">
            <summary>
            EwsServerRequest data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmEventTypeDb">
            <summary>
            EwsAlarmEventType data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmItemDb">
            <summary>
            EwsAlarmItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmEventDb">
            <summary>
            EwsAlarmEvent data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.EnumItemDb">
            <summary>
            EwsEnumItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SubscriptionDb">
            <summary>
            EwsSubscription data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SubscriptionItemDb">
            <summary>
            EwsSubscriptionItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.NotificationDb">
            <summary>
            EwsNotificationtion data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.NotificationItemDb">
            <summary>
            EwsNotificationtionItem data context.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.Users">
            <summary>
            Returns an IQueryable for the Users for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ContainerItems">
            <summary>
            Returns an IQueryable for the ContainerItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ValueItems">
            <summary>
            Returns an IQueryable for the ValueItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.HistoryItems">
            <summary>
            Returns an IQueryable for the HistoryItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.HistoryRecords">
            <summary>
            Returns an IQueryable for the HistoryRecords for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmEventTypes">
            <summary>
            Returns an IQueryable for the AlarmEventTypes for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmItems">
            <summary>
            Returns an IQueryable for the AlarmItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AlarmEvents">
            <summary>
            Returns an IQueryable for the AlarmEvents for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.EnumItems">
            <summary>
            Returns an IQueryable for the EnumItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.Subscriptions">
            <summary>
            Returns an IQueryable for the Subscriptions for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.SubscriptionItems">
            <summary>
            Returns an IQueryable for the SubscriptionItems for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.Notifications">
            <summary>
            Returns an IQueryable for the Notifications for the configured Server
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.NotificationItems">
            <summary>
            Returns an IQueryable for the NotificationItems for the configured Server
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.StartServer">
            <summary>
            Starts the Server by creating an asynchronous EwsServerRequest.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.StopServer">
            <summary>
            Stops the Server by creating an asynchronous EwsServerRequest.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.PurgeData">
            <summary>
            Purges all data contents, but not users, from the connected Server
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.PurgeAll">
            <summary>
            Purges all contents including all users except the connected user.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerAllowCookies(System.Boolean)">
            <summary>
            Modifys the AllowCookies of the current Server to the value supplied.
            </summary>
            <param name="newAllowCookies">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerIsAutoStart(System.Boolean)">
            <summary>
            Modifys the IsActive of the current Server to the value supplied.
            </summary>
            <param name="newIsAutoStart">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerName(System.String)">
            <summary>
            Modifys the Name of the current Server to the value supplied.
            </summary>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerAddress(System.String)">
            <summary>
            Modifys the Address of the current Server to the value supplied.
            </summary>
            <param name="newAddress">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerRealm(System.String)">
            <summary>
            Modifys the Realm of the current Server to the value supplied.
            </summary>
            <remarks>
            Note, already running EwsServers will need to be stopped and re-started in order to use the new Realm.
            </remarks>
            <param name="newRealm">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerPageSize(System.Int32)">
            <summary>
            Modifys the Realm of the current Server to the value supplied.
            </summary>
            <remarks>
            Note, already running EwsServers will need to be stopped and re-started in order to use the new Address.
            </remarks>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerRootContainerItemAlternateId(System.String)">
            <summary>
            Modifys the RootContainerItemAlternateId of the current Server to the value supplied.
            </summary>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerRootContainerItemDescription(System.String)">
            <summary>
            Modifys the RootContainerItemDescription of the current Server to the value supplied.
            </summary>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerSupportedMethods(Mongoose.Ews.Server.Data.Shared.EwsServerMethods)">
            <summary>
            Modifys the supported methods of the current Server to the value supplied.
            </summary>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerSupportedHashAlgorithms(Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms)">
            <summary>
            Modifys the supported HashAlgorithm of the current Server to the value supplied.
            </summary>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyMaxRecievedMessageSize(System.Int32)">
            <summary>
            Modifys the MaxRecievedMessageSize of the current Server to the value supplied.
            </summary>
            <param name="newMaxRecievedMessageSize">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteServer">
            <summary>
            Deletes the current Server instance and all its children.  Not reversible!  Use caution!
            As side effect is that once you've deleted the authenticated server, your reference to this EwsServerDataAdapter is rendered useless and you will need to instantiate a new reference.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddUser(System.String,System.String,System.Boolean)">
            <summary>
            Adds a new User to the connected Server.
            </summary>
            <param name="username">Name of the new user</param>
            <param name="password">Password of the new user</param>
            <param name="isActive">Whether the new user will be immediately acitve or no</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddUser_Internal(Mongoose.Ews.Server.Data.EwsServer,System.String,System.String,System.Boolean)">
            <summary>
            Adds a new User to the connected Server without any precondition assertions.  Only for bootstrapping a new Server!  
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserUsername(Mongoose.Ews.Server.Data.EwsUser,System.String)">
            <summary>
            Modifys the supplied User by changing it's Username.
            </summary>
            <param name="toModify">User to modify</param>
            <param name="newUserName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserUsername(System.Int32,System.String)">
            <summary>
            Modifys the referenced User by changing it's Username.
            </summary>
            <param name="userId">Id of the User to modify</param>
            <param name="newUserName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserPassword(Mongoose.Ews.Server.Data.EwsUser,System.String)">
            <summary>
            Modifys the supplied User by changing it's Password.
            </summary>
            <param name="toModify">User to modify</param>
            <param name="newPassword">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserPassword(System.Int32,System.String)">
            <summary>
            Modifys the refereneced User by changing it's Password.
            </summary>
            <param name="userId">Id of the User to modify</param>
            <param name="newPassword">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserIsActive(Mongoose.Ews.Server.Data.EwsUser,System.Boolean)">
            <summary>
            Modifys the supplied User by changing it's IsActive.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyUserIsActive(System.Int32,System.Boolean)">
            <summary>
            Modifys the refereneced User by changing it's IsActive.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteUser(Mongoose.Ews.Server.Data.EwsUser)">
            <summary>
            Permanantly removes the user.
            </summary>
            <param name="toDelete">User to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteUser(System.Int32)">
            <summary>
            Permanantly removes the user.
            </summary>
            <param name="userId">Id of the user to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteUser(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsUser})">
            <summary>
            Permanantly removes all Users supplied.
            </summary>
            <param name="toDelete">Users to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyCommonDataItemParent``1(``0,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Common.Data.IDataSource{``0},System.Action)">
            <summary>
            Common generic method for reparenting any EwsCommonDataItem
            </summary>
            <typeparam name="T">Type of EwsCommonDataItem</typeparam>
            <param name="toModify">Item to modify</param>
            <param name="newParent">New parent (or null)</param>
            <param name="dataSource">IDataSource to handle commits</param>
            <param name="preSaveAction">If further non-standard pre save action is required pass it as a delegate.  Can modify toModify and/or do further validation.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyCommonDataItemAlternateId``1(``0,System.String,Mongoose.Common.Data.IDataSource{``0})">
            <summary>
            Common generic method for changing the alternateId of any EwsCommonDataItem.
            </summary>
            <typeparam name="T">Type of EwsCommonDataItem</typeparam>
            <param name="toModify">Item to modify</param>
            <param name="newAlternateId">New alternateId</param>
            <param name="dataSource">IDataSource to handle commits</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyCommonDataItemName``1(``0,System.String,Mongoose.Common.Data.IDataSource{``0})">
            <summary>
            Common generic method for changing the name of any INamedEwsCommonDataModelItem EwsCommonDataItem.
            </summary>
            <typeparam name="T">Type of EwsCommonDataItem</typeparam>
            <param name="toModify">Item to modify</param>
            <param name="newName">New name</param>
            <param name="dataSource">IDataSource to handle commits</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyServerItemDescription``1(``0,System.String,Mongoose.Common.Data.IDataSource{``0})">
            <summary>
            Common generic method for changing the name of any INamedEwsCommonDataModelItem EwsCommonDataItem.
            </summary>
            <typeparam name="T">Type of EwsCommonDataItem</typeparam>
            <param name="toModify">Item to modify</param>
            <param name="newDescription">New name</param>
            <param name="dataSource">IDataSource to handle commits</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddContainerItem(System.String,System.String,System.String,Ews.Common.EwsContainerTypeEnum,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Adds a new ContainerItem to the connected Server.
            </summary>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="type">Type of the item.</param>
            <param name="parent">Optional parent ContainerItem</param>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemAlternateId(Mongoose.Ews.Server.Data.EwsContainerItem,System.String)">
            <summary>
            Modifys the supplied ContainerItem by changing it's AlternateId.
            </summary>
            <param name="toModify">ContainerItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemAlternateId(System.String,System.String)">
            <summary>
            Modifys the refereneced ContainerItem by changing it's AlternateId.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemName(Mongoose.Ews.Server.Data.EwsContainerItem,System.String)">
            <summary>
            Modifys the supplied ContainerItem by changing it's Name.
            </summary>
            <param name="toModify">ContainerItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemName(System.String,System.String)">
            <summary>
            Modifys the refereneced ContainerItem by changing it's Name.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemDescription(Mongoose.Ews.Server.Data.EwsContainerItem,System.String)">
            <summary>
            Modifys the supplied ContainerItem by changing it's AlternateId.
            </summary>
            <param name="toModify">ContainerItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemDescription(System.String,System.String)">
            <summary>
            Modifys the refereneced ContainerItem by changing it's AlternateId.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemType(Mongoose.Ews.Server.Data.EwsContainerItem,Ews.Common.EwsContainerTypeEnum)">
            <summary>
            Modifys the supplied ContainerItem by changing it's Type.
            </summary>
            <param name="toModify">ContainerItem to modify</param>
            <param name="newType">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemType(System.String,Ews.Common.EwsContainerTypeEnum)">
            <summary>
            Modifys the refereneced ContainerItem by changing it's Type.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newType">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemParent(Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the supplied ContainerItem by changing it's Parent.
            </summary>
            <param name="toModify">ContainerItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyContainerItemParent(System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the refereneced ContainerItem by changing it's Parent.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteContainerItem(Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Permanantly removes the ContainerItem.
            </summary>
            <param name="toDelete">ContainerItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteContainerItem(System.String)">
            <summary>
            Permanantly removes the referenced ContainerItem.
            </summary>
            <param name="containerItemAlternateId">AlternateId of the ContainerItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteContainerItem(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsContainerItem})">
            <summary>
            Permanantly removes all ContainerItems supplied.
            </summary>
            <param name="toDelete">ContainerItems to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.String,System.String,System.String,Ews.Common.EwsValueTypeEnum,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="type">Type of the item.</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.String,System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.Boolean},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.DateTime},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.Double},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.Int64},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.Int32},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueItem(System.Nullable{System.TimeSpan},System.String,System.String,System.String,Ews.Common.EwsValueWriteableEnum,Ews.Common.EwsValueForceableEnum,Ews.Common.EwsValueStateEnum,System.String,Mongoose.Ews.Server.Data.EwsContainerItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Adds a new ValueItem to the connected Server.
            </summary>
            <param name="value">Initial Value</param>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="writeable">Whether the item can be "Set"</param>
            <param name="forceable">Whether the item can be "Forced"</param>
            <param name="state">State of the item</param>
            <param name="unit">Unit of the item</param>
            <param name="parent">Parent of the item</param>
            <param name="enumItem">Enum associated with the item.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemAlternateId(Mongoose.Ews.Server.Data.EwsValueItem,System.String)">
            <summary>
            Modifys the supplied ValueItem by changing it's AlternateId.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemAlternateId(System.String,System.String)">
            <summary>
            Modifys the refereneced ValueItem by changing it's AlternateId.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemForceable(Mongoose.Ews.Server.Data.EwsValueItem,Ews.Common.EwsValueForceableEnum)">
            <summary>
            Modifys the supplied ValueItem by changing it's Writeable.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newForceable">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemForceable(System.String,Ews.Common.EwsValueForceableEnum)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Writeable.
            </summary>
            <param name="valueItemAlternateId">ValueItem of the ValueItem to modify</param>
            <param name="newForceable">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemName(Mongoose.Ews.Server.Data.EwsValueItem,System.String)">
            <summary>
            Modifys the supplied ValueItem by changing it's Name.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemName(System.String,System.String)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Name.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemDescription(Mongoose.Ews.Server.Data.EwsValueItem,System.String)">
            <summary>
            Modifys the supplied ValueItem by changing it's Description.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemDescription(System.String,System.String)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Description.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemEnum(Mongoose.Ews.Server.Data.EwsValueItem,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Modifys the supplied ValueItem by changing it's Enum.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newEnumItem">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemEnum(System.String,Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Enum.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newEnumItem">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemType(Mongoose.Ews.Server.Data.EwsValueItem,Ews.Common.EwsValueTypeEnum)">
            <summary>
            Modifys the supplied ValueItem by changing it's Type.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newType">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemType(System.String,Ews.Common.EwsValueTypeEnum)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Type.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newType">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemWriteable(Mongoose.Ews.Server.Data.EwsValueItem,Ews.Common.EwsValueWriteableEnum)">
            <summary>
            Modifys the supplied ValueItem by changing it's Writeable.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newWriteable">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemWriteable(System.String,Ews.Common.EwsValueWriteableEnum)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Writeable.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newWriteable">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemState(Mongoose.Ews.Server.Data.EwsValueItem,Ews.Common.EwsValueStateEnum)">
            <summary>
            Modifys the supplied ValueItem by changing it's State.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newState">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemState(System.String,Ews.Common.EwsValueStateEnum)">
            <summary>
            Modifys the refereneced ValueItem by changing it's State.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newState">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemParent(Mongoose.Ews.Server.Data.EwsValueItem,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the supplied ValueItem by changing it's Parent.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemParent(System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the refereneced ValueItem by changing it's Parent.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ContainerItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.String,System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.String,System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Boolean},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.Boolean},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.DateTime},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.DateTime},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Double},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.Double},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Int64},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.Int64},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Int32},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.Int32},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.TimeSpan},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the supplied ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{System.TimeSpan},System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value to "empty".  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemValue(System.String,System.Nullable{Ews.Common.EwsValueStateEnum})">
            <summary>
            Modifys the referenced ValueItem by changing it's Value to "empty".  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newState">New State value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemUnit(Mongoose.Ews.Server.Data.EwsValueItem,System.String)">
            <summary>
            Modifys the supplied ValueItem by changing it's Unit.
            </summary>
            <param name="toModify">ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyValueItemUnit(System.String,System.String)">
            <summary>
            Modifys the referenced ValueItem by changing it's Unit.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.String)">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.String)">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Boolean})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.Boolean})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.DateTime})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.DateTime})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Double})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.Double})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Int64})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.Int64})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.Int32})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.Int32})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(Mongoose.Ews.Server.Data.EwsValueItem,System.Nullable{System.TimeSpan})">
            <summary>
            Forces the supplied ValueItem to the given value.
            </summary>
            <param name="toModify">ValueItem to force.</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ForceValueItem(System.String,System.Nullable{System.TimeSpan})">
            <summary>
            Forces the refereneced ValueItem to the given value.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
            <param name="newValue">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.UnforceValueItem(Mongoose.Ews.Server.Data.EwsValueItem)">
            <summary>
            Unforces the supplied ValueItem.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="toModify">ValueItem to modify</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.UnforceValueItem(System.String)">
            <summary>
            Unforces the supplied ValueItem.  Creates HistoryRecords for any HistoryItem that IsActive
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to modify</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddHistoryRecordsAsNeeded(Mongoose.Ews.Server.Data.EwsValueItem)">
            <summary>
            Called when a EwsValueItem Value or State is modified.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddHistoryRecordsManually(System.String,System.Collections.Generic.List{System.Tuple{Ews.Common.EwsValueStateEnum,System.DateTime,System.String}})">
            <summary>
            Adds multiple EwsHistoryRecord for the supplied EwsHistoryItem manually giving you complete control of Value, State, and Timestap.
            </summary>
            <param name="ewsHistoryItemAlternateId">AlternateId of an EwsHistoryItem for which history will be added.</param>
            <param name="historyTuples">List of Tuple of State, Timestamp, and Value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddHistoryRecordsManually(Mongoose.Ews.Server.Data.EwsHistoryItem,System.Collections.Generic.List{System.Tuple{Ews.Common.EwsValueStateEnum,System.DateTime,System.String}})">
            <summary>
            Adds multiple EwsHistoryRecord for the supplied EwsHistoryItem manually giving you complete control of Value, State, and Timestap.
            </summary>
            <param name="ewsHistoryItem">EwsHistoryItem for which history will be added.</param>
            <param name="historyTuples">List of Tuple of State, Timestamp, and Value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteValueItem(Mongoose.Ews.Server.Data.EwsValueItem)">
            <summary>
            Permanantly removes the ValueItem.
            </summary>
            <param name="toDelete">ValueItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteValueItem(System.String)">
            <summary>
            Permanantly removes the referenced ValueItem.
            </summary>
            <param name="valueItemAlternateId">AlternateId of the ValueItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteValueItem(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsValueItem})">
            <summary>
            Permanantly removes all ValueItems supplied.
            </summary>
            <param name="toDelete">ValueItems to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddHistoryItem(System.String,System.String,System.String,Mongoose.Ews.Server.Data.EwsValueItem,System.Boolean,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Adds a new HistoryItem to the connected Server.
            </summary>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="valueItem">Referenced ValueItem</param>
            <param name="isActive">History is being recorded</param>
            <param name="parent">Parent of the item</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemAlternateId(Mongoose.Ews.Server.Data.EwsHistoryItem,System.String)">
            <summary>
            Modifys the supplied HistoryItem by changing it's AlternateId.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemAlternateId(System.String,System.String)">
            <summary>
            Modifys the refereneced HistoryItem by changing it's AlternateId.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemName(Mongoose.Ews.Server.Data.EwsHistoryItem,System.String)">
            <summary>
            Modifys the supplied HistoryItem by changing it's Name.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemName(System.String,System.String)">
            <summary>
            Modifys the referenced HistoryItem by changing it's Name.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemDescription(Mongoose.Ews.Server.Data.EwsHistoryItem,System.String)">
            <summary>
            Modifys the supplied HistoryItem by changing it's Description.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemDescription(System.String,System.String)">
            <summary>
            Modifys the referenced HistoryItem by changing it's Description.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemParent(Mongoose.Ews.Server.Data.EwsHistoryItem,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the supplied HistoryItem by changing it's Parent.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemParent(System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the referenced HistoryItem by changing it's Parent.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemIsActive(Mongoose.Ews.Server.Data.EwsHistoryItem,System.Boolean)">
            <summary>
            Modifys the supplied HistoryItem by changing it's IsActive.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="newIsActive">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyHistoryItemIsActive(System.String,System.Boolean)">
            <summary>
            Modifys the referenced HistoryItem by changing it's IsActive.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="newIsActive">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteHistoryItem(Mongoose.Ews.Server.Data.EwsHistoryItem)">
            <summary>
            Permanantly removes the HistoryItem.
            </summary>
            <param name="toDelete">HistoryItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteHistoryItem(System.String)">
            <summary>
            Permanantly removes the referenced HistoryItem.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteHistoryItem(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsHistoryItem})">
            <summary>
            Permanantly removes all AlarmItems supplied.
            </summary>
            <param name="toDelete">AlarmItems to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteHistoryRecords(Mongoose.Ews.Server.Data.EwsHistoryItem,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Deletes a range of HistoryRecord instances for toModify where the Timestamp is in the supplied range.
            </summary>
            <param name="toModify">HistoryItem to modify</param>
            <param name="from">Deletes HistoryRecord with Timestamp GE this value</param>
            <param name="to">Deletes HistoryRecord with Timestamp LT this value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteHistoryRecords(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Deletes a range of HistoryRecord instances for the HistoryItem with the supplied alternateId where the Timestamp is in the supplied range.
            </summary>
            <param name="historyItemAlternateId">AlternateId of the HistoryItem to modify</param>
            <param name="from">Deletes HistoryRecord with Timestamp GE this value</param>
            <param name="to">Deletes HistoryRecord with Timestamp LT this value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddAlarmEventType(System.String,System.String)">
            <summary>
            Adds a new AlarmEventType to the connected Server.
            </summary>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmEventTypeName(Mongoose.Ews.Server.Data.EwsAlarmEventType,System.String)">
            <summary>
            Modifys the supplied AlarmEventType by changing it's Name.
            </summary>
            <param name="toModify">AlarmEventType to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmEventTypeName(System.Int32,System.String)">
            <summary>
            Modifys the referenced AlarmEventType by changing it's Description.
            </summary>
            <param name="alarmEventTypeId">Id of the AlarmEventType to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmEventTypeDescription(Mongoose.Ews.Server.Data.EwsAlarmEventType,System.String)">
            <summary>
            Modifys the supplied AlarmEventType by changing it's Description.
            </summary>
            <param name="toModify">AlarmEventType to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmEventTypeDescription(System.Int32,System.String)">
            <summary>
            Modifys the referenced AlarmEventType by changing it's Description.
            </summary>
            <param name="alarmEventTypeId">Id of the AlarmEventType to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmEventType(Mongoose.Ews.Server.Data.EwsAlarmEventType)">
            <summary>
            Permanantly removes the AlarmEventType.
            </summary>
            <param name="toDelete">AlarmEventType to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmEventType(System.Int32)">
            <summary>
            Permanantly removes the referenced AlarmEventType.
            </summary>
            <param name="alarmEventTypeId">Id of the AlarmEventType to modify</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddAlarmItem(System.String,System.String,System.String,Mongoose.Ews.Server.Data.EwsValueItem,Ews.Common.AlarmTransitionModel,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Adds a new AlarmItem to the connected Server.
            </summary>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="description">Description of the item</param>
            <param name="valueItem">Referenced ValueItem</param>
            <param name="transitionModel">TransitionModel supported</param>
            <param name="parent">Parent of the item</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemAlternateId(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String)">
            <summary>
            Modifys the supplied AlarmItem by changing it's AlternateId.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemAlternateId(System.String,System.String)">
            <summary>
            Modifys the refereneced AlarmItem by changing it's AlternateId.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="newAlternateId">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemName(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String)">
            <summary>
            Modifys the supplied AlarmItem by changing it's Name.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemName(System.String,System.String)">
            <summary>
            Modifys the referenced AlarmItem by changing it's Name.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemDescription(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String)">
            <summary>
            Modifys the supplied AlarmItem by changing it's Description.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemDescription(System.String,System.String)">
            <summary>
            Modifys the referenced AlarmItem by changing it's Description.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="newDescription">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemParent(Mongoose.Ews.Server.Data.EwsAlarmItem,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the supplied AlarmItem by changing it's Parent.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemParent(System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the referenced AlarmItem by changing it's Parent.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem)">
            <summary>
            Permanantly removes the AlarmItem.
            </summary>
            <param name="toDelete">AlarmItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmItem(System.String)">
            <summary>
            Permanantly removes the referenced AlarmItem.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmItem(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsAlarmItem})">
            <summary>
            Permanantly removes all AlarmItems supplied.
            </summary>
            <param name="toDelete">AlarmItems to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmEvents(Mongoose.Ews.Server.Data.EwsAlarmItem,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Deletes a range of AlarmEvent instances for item where the TimeStampTransation is in the supplied range.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="from">Deletes AlarmEvent with TransitionDateTime GE this value</param>
            <param name="to">Deletes AlarmEvent with TransitionDateTime LT this value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteAlarmEvents(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Deletes a range of AlarmEvent instances for item where the TimeStampTransation is in the supplied range.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="from">Deletes AlarmEvent with TransitionDateTime GE this value</param>
            <param name="to">Deletes AlarmEvent with TransitionDateTime LT this value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AcknowledgeAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the current state is being acknowledged.
            If message is null, then the message will be that of the last transition to "Active"
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AcknowledgeAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the current state is being acknowledged.
            If message is null, then the message will be that of the last transition to "Active"
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AcknowledgeAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the current state is being acknowledged.
            If message is null, then the message will be that of the last transition to "Active"
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AcknowledgeAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the current state is being acknowledged.
            If message is null, then the message will be that of the last transition to "Active"
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ActivateAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to Active state from it's current state.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ActivateAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to Active state from it's current state.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ActivateAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to Active state from it's current state.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ActivateAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to Active state from it's current state.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DisableAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to Disable state from it's current state.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DisableAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to Disable state from it's current state.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DisableAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to Disable state from it's current state.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DisableAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to Disable state from it's current state.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ResetAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the underlying value has returned to Normal.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ResetAlarmItem(Mongoose.Ews.Server.Data.EwsAlarmItem,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the underlying value has returned to Normal.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ResetAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the underlying value has returned to Normal.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ResetAlarmItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Transitions the AlarmItem to the appropriate state based on the AlarmItem's current mode when the underlying value has returned to Normal.
            </summary>
            <param name="alarmItemAlternateId">AlternateId of the AlarmItem to modify</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemState(Mongoose.Ews.Server.Data.EwsAlarmItem,Ews.Common.EwsAlarmStateEnum,System.String,Ews.Common.EwsAlarmAcknowledgeableEnum,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32)">
            <summary>
            Modifys the supplied AlarmItem's State and generates an AlarmEvent to record the transition with the priority and type supplied.
            NOTE: This is only allowed for an AlarmItem with an Unrestricted TransitionModel.  For all other models, you must use the DisableAlarmItem, ResetAlarmItem, AcknowledgeAlarmItem or ActivateAlarmItem entry point.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newValue">New State value</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="acknowledgeable">Indicates if the AlarmEvent will be acknowledgeable</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyAlarmItemState(Mongoose.Ews.Server.Data.EwsAlarmItem,Ews.Common.EwsAlarmStateEnum,System.String,Ews.Common.EwsAlarmAcknowledgeableEnum,Mongoose.Ews.Server.Data.EwsAlarmEventType,System.Int32,System.DateTime)">
            <summary>
            Modifys the supplied AlarmItem's State and generates an AlarmEvent to record the transition with the priority and type supplied.
            NOTE: This is only allowed for an AlarmItem with an Unrestricted TransitionModel.  For all other models, you must use the DisableAlarmItem, ResetAlarmItem, AcknowledgeAlarmItem or ActivateAlarmItem entry point.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newValue">New State value</param>
            <param name="message">Message for the AlarmEvent</param>
            <param name="acknowledgeable">Indicates if the AlarmEvent will be acknowledgeable</param>
            <param name="type">Type for the AlarmEvent</param>
            <param name="priority">Priority for the AlarmEvent</param>
            <param name="occurrenceTime">User defined time for when this event occurred</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.InferOccurrenceTime(System.Int32,System.DateTime)">
            <summary>
            Looks back in the event history of the AlarmItem referenced and returns the last Active transition time or now if none was found.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddEnumItem(System.String,System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Adds a new EnumItem to the connected Server.
            </summary>
            <param name="alternateId">Id served over EWS</param>
            <param name="name">Name of the item</param>
            <param name="parent">Parent of the item</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemName(Mongoose.Ews.Server.Data.EwsEnumItem,System.String)">
            <summary>
            Modifys the supplied EnumItem by changing it's Name.
            </summary>
            <param name="toModify">AlarmItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemName(System.String,System.String)">
            <summary>
            Modifys the referenced EnumItem by changing it's Name.
            </summary>
            <param name="enumItemAlternateId">AlternateId of the EnumItem to modify</param>
            <param name="newName">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemParent(Mongoose.Ews.Server.Data.EwsEnumItem,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the supplied EnumItem by changing it's Parent.
            </summary>
            <param name="toModify">EnumItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemParent(System.String,Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Modifys the referenced EnumItem by changing it's Parent.
            </summary>
            <param name="enumItemAlternateId">AlternateId of the EnumItem to modify</param>
            <param name="newParent">New value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemAddValue(Mongoose.Ews.Server.Data.EwsEnumItem,System.String,System.Int32)">
            <summary>
            Modifys the supplied EnumItem by adding a value to the list of values.
            </summary>
            <param name="toModify">EnumItem to modify</param>
            <param name="valueText">Enums text value</param>
            <param name="valueValue">Enums enumerated value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemAddValue(System.String,System.String,System.Int32)">
            <summary>
            Modifys the referenced EnumItem by adding a value to the list of values.
            </summary>
            <param name="enumItemAlternateId">AlternateId of the EnumItem to modify</param>
            <param name="valueText">Enums text value</param>
            <param name="valueValue">Enums enumerated value</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemRemoveValue(Mongoose.Ews.Server.Data.EwsEnumItem,System.String)">
            <summary>
            Modifys the supplied EnumItem by removing a value from the list of values.
            </summary>
            <param name="toModify">EnumItem to modify</param>
            <param name="valueText">Text value to be removed</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ModifyEnumItemRemoveValue(System.String,System.String)">
            <summary>
            Modifys the referenced EnumItem by removing a value from the list of values.
            </summary>
            <param name="enumItemAlternateId">AlternateId of the EnumItem to modify</param>
            <param name="valueText">Text value to be removed</param>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteEnumItem(Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Permanantly removes the EnumItem.
            </summary>
            <param name="toDelete">EnumItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteEnumItem(System.String)">
            <summary>
            Permanantly removes the referenced EnumItem.
            </summary>
            <param name="enumItemAlternateId">AlternateId of the EnumItem to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteEnumItem(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsEnumItem})">
            <summary>
            Permanantly removes all EnumItems supplied.
            </summary>
            <param name="toDelete">EnumItems to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddValueSubscription(System.Int32,System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsValueItem})">
            <summary>
            Creates a value/state change subscription to the supplied ValueItems.
            </summary>
            <param name="durationInMinutes">The duration the subscription is to remain active for.</param>
            <param name="itemsToMonitor">ValueItem to monitor as part of this subscription.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddAlarmSubscription(System.Int32,System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsAlarmItem},System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            Creates an AlarmEvent subscription to the supplied AlarmItems.
            </summary>
            <param name="durationInMinutes">The duration the subscription is to remain active for.</param>
            <param name="itemsToMonitor">AlarmItem which are being subscribed to.</param>
            <param name="priorityFrom">Optional lower end of a Prioirty filter.</param>
            <param name="priorityTo">Optional upper limit of a Priority filter.</param>
            <param name="eventTypes">Optional AlarmEventType filter.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AddHierarchySubscription(System.Int32,System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsCommonDataItem{System.Int32}})">
            <summary>
            Creates a HierarchyChanged event on the items supplied.
            </summary>
            <param name="durationInMinutes">The duration the subscription is to remain active for.</param>
            <param name="itemsToMonitor">Items to monitor</param>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RenewSubscription(Mongoose.Ews.Server.Data.EwsSubscription,System.Int32)">
            <summary>
            Renews the subscription by the duration supplied.
            </summary>
            <param name="toModify">Subscription to renew.</param>
            <param name="durationInMinutes">Duration to renew the subscription for.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RenewSubscription(System.String,System.Int32)">
            <summary>
            Renews the subscription by the duration supplied.
            </summary>
            <param name="subscriptionAlternateId">SubscriptionId of the subscription to renew.</param>
            <param name="durationInMinutes">Duration to renew the subscription for.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.TerminateSubscription(Mongoose.Ews.Server.Data.EwsSubscription)">
            <summary>
            Terminates the subscription supplied.
            </summary>
            <param name="toModify">Subscription to terminate</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.TerminateSubscription(System.String)">
            <summary>
            Terminates the subscription supplied.
            </summary>
            <param name="subscriptionAlternateId">SubscriptionId of the subscription to terminate.</param>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.UpdateSubscriptions">
            <summary>
            Checks all Active subscriptions for expiration and updates the status as needed
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteSubscription(Mongoose.Ews.Server.Data.EwsSubscription)">
            <summary>
            Permanantly removes the EwsSubscription.
            </summary>
            <param name="toDelete">Subscription to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteSubscription(System.String)">
            <summary>
            Permanantly removes the referenced Subscription.
            </summary>
            <param name="subscriptionAlternateId">AlternateId of the Subscription to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteSubscription(System.Collections.Generic.List{Mongoose.Ews.Server.Data.EwsSubscription})">
            <summary>
            Permanantly removes all EwsSubscriptions supplied.
            </summary>
            <param name="toDelete">EwsSubscriptions to delete</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteNotification(System.String)">
            <summary>
            Deletes the referenced EwsNotification.
            </summary>
            <param name="notificationAlternateId">AlternateId of the Notification to delete.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteNotification(Mongoose.Ews.Server.Data.EwsNotification)">
            <summary>
            Deletes the referenced EwsNotification.
            </summary>
            <param name="notification">Notification to delete.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteNotifications(System.String)">
            <summary>
            Deletes EwsNotifications which are not the current or next Notification for EwsSubscription whose alternateId is supplied.
            </summary>
            <param name="subscriptionAlternateId">AlternateId of the Subscription to delete.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.DeleteNotifications(Mongoose.Ews.Server.Data.EwsSubscription)">
            <summary>
            Deletes EwsNotifications which are not the current or next Notification for the supplied subscription
            </summary>
            <param name="subscription">Subscription to purge Notifications for.</param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.CreateNotificationSession(Mongoose.Ews.Server.Data.EwsSubscription,System.String)">
            <summary>
            Creates a new notification session for the supplied subscription and returns it.
            </summary>
            <param name="subscription">The Subscription the Notification session is being generated for.</param>
            <param name="notificationSessionAlternateId">
            The AlternateId value for the EWsNotification.  
            If NOT supplied, then it is assumed that a new "alpha" request is being made and all subscribed items will be reported on.
            If supplied, then a new NOtificationSession will be created (with this AlternateId) including all detected changes since the Subscriptions LastNotification.
            </param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.CreateNotificationSession(System.String,System.String)">
            <summary>
            Creates a new notification session for the supplied subscription and returns it.
            </summary>
            <param name="subscriptionAlternateId">The AlternateId of the Subscription the Notification session is being generated for.</param>
            <param name="notificationSessionAlternateId">
            The AlternateId value for the EWsNotification.  
            If NOT supplied, then it is assumed that a new "alpha" request is being made and all subscribed items will be reported on.
            If supplied, then a new NOtificationSession will be created (with this AlternateId) including all detected changes since the Subscriptions LastNotification.
            </param>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.FindCommonDataModelItemByAlternateId(System.String)">
            <summary>
            Returns the proper ContainerItem, ValueItem, HistoryItem, AlarmItem, or EnumItem with the alternateId supplied.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.FindCommonDataModelItemByAlternateId(System.String,Ews.Common.EwsDataModelTypeEnum@)">
            <summary>
            Returns the proper ContainerItem, ValueItem, HistoryItem, AlarmItem, or EnumItem with the alternateId supplied.
            Returns the type of the item as an output parameter
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.FindCommonDataModelItemsByAlternateId(System.Collections.Generic.List{System.String})">
            <summary>
            Returns all ContainerItem, ValueItem, HistoryItem, AlarmItem, or EnumItem from the list of alternateIds
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertIsConnected">
            <summary>
            Asserts that this EwsServerDataAdapter has been Connected (and authenticated) to use a specific Server.  
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertNoCrossServerAccess(Mongoose.Ews.Server.Data.IEwsServerItem)">
            <summary>
            Asserts that the item supplied can belong to the current Server
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertNoCrossServerAccess``1(System.Collections.Generic.IList{``0})">
            <summary>
            Asserts that all items supplied can belong to the current Server
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertAlternateIdNotUsed(System.String)">
            <summary>
            Asserts that the alternateId supplied is not in use by any object for the configured Server instance.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertEntityNotDeleted(System.Object)">
            <summary>
            Asserts that the entity supplied is not NULL (infers deletion).
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertEwsAlarmEventTypeNameNotUsed(System.String)">
            <summary>
            Asserts that the name supplied is not used for an existing EwsAlarmEventType instance.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertEwsServerNameNotUsed(System.String)">
            <summary>
            Asserts that the name supplied is not used by ANY Server instance.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertEwsServerAddressNotUsed(System.String)">
            <summary>
            Asserts that the address supplied is not used by ANY Server instance.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertNameNotUsedInParentContainerItem(Mongoose.Ews.Server.Data.EwsContainerItem,System.String)">
            <summary>
            Asserts that the name supplied is not used by any other object within the context of the supplied parent.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertNotAllowed(System.Boolean)">
            <summary>
            If condition is true, then this will throw a Not Allowed exception
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertIsNotCurrentUser(Mongoose.Ews.Server.Data.EwsUser)">
            <summary>
            Asserts that the user passed is not the AuthenticatedUser
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertUserIsDeleteable(Mongoose.Ews.Server.Data.EwsUser)">
            <summary>
            Asserts that the user supplied can be deleted.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertUserNameNotUsed(System.String)">
            <summary>
            Asserts that the name supplied is not already used in the current Server.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertUserIsntOnlyUser(Mongoose.Ews.Server.Data.EwsUser)">
            <summary>
            Assert that there is at least one other User in the configured Server
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertIsValidPassword(System.String)">
            <summary>
            Asserts that the password supplied meets the minimum complexity requirements.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertIsValidUsername(System.String)">
            <summary>
            Asserts that the password supplied meets the minimum complexity requirements.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.AssertSubscriptionIsActive(Mongoose.Ews.Server.Data.EwsSubscription)">
            <summary>
            Asserts that the subscription has a Status of Active.
            Throws an ApplicationException if not the case.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.IsValidPassword(System.String)">
            <summary>
            Returns true if the password supplied meets the required strength criteria.  Passwords must the 8 characters long and contain at at least 3 of the following: uppercase, lowercase, number and non-alphanumerics.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.IsValidUsername(System.String)">
            <summary>
            Returns true if the username supplied meets the required strength criteria.  Usernames must the 5 characters long.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsAlarmEvent)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsAlarmEventType)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsAlarmItem)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsContainerItem)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsHistoryItem)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsServer)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsUser)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsValueItem)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.RevertChanges(Mongoose.Ews.Server.Data.EwsEnumItem)">
            <summary>
            Reverts uncommitted changes to the item.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerDataAdapter.VocabularyMode">
            <summary>
            Controls how error messages and such are conveyed to the consumer.  Local by default.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerDataAdapter.ReplaceTokens(System.String)">
            <summary>
            Instance helper.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServerItem`1">
            <summary>
            Any item which is "owned" by an EwsServer but we're not modelling as strict Parent-Child relationship in our domain model.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.Shared.EwsServerMethods">
            <summary>
            Models the methods supported by the parent EwsServer instance.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerRequest.RequestType">
            <summary>
            What is being requested.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerRequest.RequestedOn">
            <summary>
            When the request was made.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerRequest.RequestedBy">
            <summary>
            The user that made the request.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerRequest.Resolution">
            <summary>
            The resolution of the request.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerRequest.CompletedOn">
            <summary>
            When the request was actually completed, not the process itself.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServerVocabManager">
            <summary>
            Vocabulary manager which replaces known vocab tokens in a string with the appropriate values based on desired context.
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Data.EwsServerVocabularyMode.Default">
            <summary>
            Default vocabulary
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Data.EwsServerVocabularyMode.Poral">
            <summary>
            Vocabulary adjusted for Poral display
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Data.EwsServerVocabularyMode.RestVocabulary">
            <summary>
            Vocabulary for REST usage
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsSubscription">
            <summary>
            Represents a Subscription for the purpose of polling for notifications when the subscribed to item(s) have changed.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.AlternateId">
            <summary>
            Unique Id of the subscription.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.CreatedOn">
            <summary>
            DateTime in UTC when this Subscription was created.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.ExpiresOn">
            <summary>
            DateTime in UTC when the subscription will expire.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.Status">
            <summary>
            The status of the subscription.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.Mode">
            <summary>
            The mode of the subscription.  Currently this is immutable and will always return "PULL-Eventing".
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.Type">
            <summary>
            Defines the type of items which are being subscribed to.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.LastNotification">
            <summary>
            The DateTime when the last GetNotification call was made.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.ActiveNotificationId">
            <summary>
            AlternateId of the current EwsNotification session.  The "active" session does exist.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.NextNotificationId">
            <summary>
            AlternateId of the next EwsNotification session.  the "next" session doesn't actually exist until CreateNotificationSession is called supplying this value.  This value exists because we are required to send
            EWS clients this value for their use the NEXT time.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.IncludeMetadata">
            <summary>
            If true, Metadata will be included in the response where applicable.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.PriorityFrom">
            <summary>
            When Subscription is for an AlarmItem or SystemEvent, this will represent the lower bound of an implicit Priority value filter.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.PriorityTo">
            <summary>
            When Subscription is for an AlarmItem or SystemEvent, this will represent the upper bound of an implicit Priority value filter.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.Types">
            <summary>
            Extension author supplied features associated with this license.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscription.TypesAsXml">
            <summary>
            XML version of Types property for persistance purposes.  Use Types property instead.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsSubscription.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <inheritdoc />>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsSubscriptionItem">
            <summary>
            Represents an item (ValueItem, AlarmItem, SystemEvent) which is being subscribed to.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscriptionItem.SubscriptionId">
            <summary>
            Id of the Subscription this item is part of.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscriptionItem.Subscription">
            <summary>
            Subscription this item is part of.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscriptionItem.AlternateId">
            <summary>
            The AlternateId of the item that is subscribed to.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsSubscriptionItem.AlternateIdType">
            <summary>
            The type of AlternateId.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsUser">
            <summary>
            Logical user for an EwsServer
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsUser.Password">
            <summary>
            The underlying value of the Parameter.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsUser.IsEncrypted">
            <summary>
            Indicates that the Password is encrypted.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsUser.GetsEncrypted">
            <summary>
            Indicates that value will be encrypted when persisted to the database.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsUser.Vector">
            <summary>
            When encrypted, this represents the InitializationVector for the encryption routine.  This value is handled purely by the ORM layer and is scrubbed outbound of it.
            Whatever you set here will be ignored.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsUser.IsValidPassword(System.String)">
            <summary>
            Returns true if the password supplied meets the required strength criteria.  Passwords must the 8 characters long and contain at at least 3 of the following: uppercase, lowercase, number and non-alphanumerics.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsUser.IsValidUsername(System.String)">
            <summary>
            Returns true if the username supplied meets the required strength criteria.  Usernames must the 5 characters long.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsValueItem.LastModified">
            <summary>
            When the ValueItem's Value and/or State was last updated
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsValueItem.GetTypeOfValue">
            <summary>
            Returns the System type which corresponds to this instances Type value.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsValueItem.GetValue">
            <summary>
             Returns the Value of this parameter as defined by Type property not T.  If the two do not jive, a runtime exception will be encounterd since you'll get a cast error.
             We need a T in order to make this a type safe call.
             </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsValueItemChanged">
            <summary>
            Represents details on a ValueItem whos Value and/or State changed
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsValueItemChanged.Value">
            <summary>
            The Value at the time of the change.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsValueItemChanged.State">
            <summary>
            The State at the time of the change.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsAlarmEventDataSource">
            <summary>
            IDataSource for EwsAlarmEvent
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsAlarmEventTypeDataSource">
            <summary>
            IDataSource for EwsAlarmEventType
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsAlarmItemDataSource">
            <summary>
            IDataSource for EwsAlarmItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsEnumItemDataSource">
            <summary>
            IDataSource for EwsEnumItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsHistoryItemDataSource">
            <summary>
            IDataSource for EwsHistoryItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServiceHost">
            <summary>
            Base class for any ServiceHost which will serve data for an EwsServer based on MongooseDataExchange.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServiceHost.#ctor(System.Type,Mongoose.Ews.Server.Data.EwsServer)">
            <summary>
            Protected constructor that should be leveraged when subclassing this EwsServiceHost.
            </summary>
            <param name="type">Type of the MongooseDataExchange subclass which will supply contract controllers to process all requests.</param>
            <param name="serverConfiguration">Native SmartConnector EwsServer instance that will be served.</param>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.ServerId">
            <summary>
            ID of the EwsServer we are hosting.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.ServerAddress">
            <summary>
            Address of the EwsServer at the time the server was spun up.  Used to detect an address change that necessitates an automatic reprovision of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.HashAlgorithms">
            <summary>
            List of supported Http digest authentication hashing algorithms of the EwsServer at the time the server was spun up.  
            Used to detect an address change that necessitates an automatic reprovision of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.Realm">
            <summary>
            Realm of the EwsServer at the time the server was spun up.  Used to detect an address change that necessitates an automatic reprovision of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.AllowCookies">
            <summary>
            AllowCookies of the EwsServer at the time the server was spun up.  Used to detect an address change that necessitates an automatic reprovision of the server.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.MaxReceivedMessageSize">
            <summary>
            The maximum recieved message size in bytes that the EWS server can receive before the request is rejected
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServiceHost.IsHttps">
            <summary>
            Returns true if HTTPS is the used in the ServerAddress.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServiceHost.OnOpening">
            <summary>
            Calls ProvisionEndpoint in this class to allow customization of the EndPoint by sub-classes.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServiceHost.ConfigureAuthenticationHandlers(Mongoose.Ews.Server.Data.EwsServer)">
            <summary>
            Configures the Authentication and Authorization managers during construction.  
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServiceHost.CreateBinding(System.Boolean)">
            <summary>
            Creates our CustomBinding.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsHistoryRecordDataSource">
            <summary>
            IDataSource for EwsHistoryRecord
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsNotificationDataSource">
            <summary>
            IDataSource for EwsNotification
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsNotificationItemDataSource">
            <summary>
            IDataSource for EwsNotificationItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsServerRequestDataSource">
            <summary>
            IDataSource for EwsServerRequest
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsSubscriptionDataSource">
            <summary>
            IDataSource for EwsSubscription
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsSubscriptionItemDataSource">
            <summary>
            IDataSource for EwsSubscriptionItem
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.IEwsTokenAuthenticator.Authenticate(System.String,System.String)">
            <summary>
            Extract the EwsServer and EwsUser instances from the DbContext based on the supplied authentication token.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.IEwsTokenAuthenticator.CreateToken(System.String,System.Int32,System.String@)">
            <summary>
            Create an authentication token which we can later use to authenticate against.
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsContainerItemDataSource">
            <summary>
            IDataSource for EwsContainerItem
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsServerDataSource">
            <summary>
            IDataSource for EwsServer
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsUserDataSource">
            <summary>
            IDataSource for EwsUser
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.IEwsValueItemDataSource">
            <summary>
            IDataSource for EwsValueItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.NotificationItemEvent.AlternateId">
            <summary>
            The AlternateId of the item whos hierarchy changed.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.NotificationItemEvent.ChangedAt">
            <summary>
            The DateTime in UTC when the Value and/or State changed
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.AlarmItemStateTransitionAlreadyInState">
            <summary>
              Looks up a localized string similar to The {0} %AlarmItem% {1} is already {2}..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.AlarmItemStateTransitionNotAllowed">
            <summary>
              Looks up a localized string similar to The {0} %AlarmItem% {1} doesn&apos;t support a transition from {2} to {3}..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.AlternateIdAlreadyInUse">
            <summary>
              Looks up a localized string similar to The %AlternateId% {0} is already in use.  %AlternateId% values must be unique..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.CannotChangeValueItemEnumWhileForced">
            <summary>
              Looks up a localized string similar to Cannot change a %ValueItem% Enum resulting in a Value change while it is Forced..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.CannotDeleteCurrentUser">
            <summary>
              Looks up a localized string similar to The current User cannot be deleted or deactivated..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.CannotDeleteOnlyUser">
            <summary>
              Looks up a localized string similar to The only User cannot be deleted or deactivated..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ChangeNotAllowedWithPendingChanges">
            <summary>
              Looks up a localized string similar to The desired action is not allowed when AutoCommit is disabled and there are pending commits..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ChangesPending">
            <summary>
              Looks up a localized string similar to Cannot perform the required operation because there are unsaved changes..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ContainerItemCannotBeItsOwnParent">
            <summary>
              Looks up a localized string similar to A %ContainerItem% cannot be its own parent..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ContainerItemDeviceChildTypeError">
            <summary>
              Looks up a localized string similar to A %ContainerItem% of type Device cannot parent a %ContainerItem% of type Service..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ContainerItemStructureChildTypeError">
            <summary>
              Looks up a localized string similar to A %ContainerItem% of type Structure can only parent other %ContainerItem% of type Structure..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.CrossServerAccessNotAllowed">
            <summary>
              Looks up a localized string similar to Cross %EwsServer% access is not allowed by the adapter..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsAlarmEventTypeNameAlreadyUsed">
            <summary>
              Looks up a localized string similar to The %AlarmEventType% Name {0} is already in use.  %AlarmEventType% Names must be unique..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsEnumNameAlreadyUsed">
            <summary>
              Looks up a localized string similar to The %EnumItem% Name {0} is already in use.  %EnumItem% Names must be unique in a logical Server..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsServer">
            <summary>
              Looks up a localized string similar to %EwsServer%.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsServerAddressAlreadyUsed">
            <summary>
              Looks up a localized string similar to A %EwsServer% with that address already exists.  Please Intialize if you wish to use that %EwsServer% or use a different address/port to create a new one..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsServerNameAlreadyUsed">
            <summary>
              Looks up a localized string similar to The %EwsServer% Name {0} already exists.  Please Intialize if you wish to use that Server or use a different name to create a new one.  %EwsServer% Names must be unique accross all %EwsServer%s..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.EwsUserAlreadyExists">
            <summary>
              Looks up a localized string similar to The User Name {0} is already in use.  User Names must be unique in a %EwsServer%..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidDateRange">
            <summary>
              Looks up a localized string similar to The supplied Date range is not valid..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidIdList">
            <summary>
              Looks up a localized string similar to Invalid ID list..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidPassword">
            <summary>
              Looks up a localized string similar to The password supplied does not meet the required length and complexity requirements..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidPriorityRange">
            <summary>
              Looks up a localized string similar to The supplied Priority range is not valid..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidTypeParameter">
            <summary>
              Looks up a localized string similar to {0} is not a valid {1} parameter..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.InvalidUsername">
            <summary>
              Looks up a localized string similar to The username supplied does not meet the required length and complexity requirements..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ItemDeleted">
            <summary>
              Looks up a localized string similar to Item was not found and may have been deleted..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ItemIsNotWriteable">
            <summary>
              Looks up a localized string similar to Item is not writeable..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NameAlreadyUsed">
            <summary>
              Looks up a localized string similar to An item with the Name {0} already exists for that parent %ContainerItem%.  Objects must have unique Name values within the parent&apos;s direct scope..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NotAllowed">
            <summary>
              Looks up a localized string similar to The method or operation you are performing is not allowed..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NotAnAcceptableEnumValue">
            <summary>
              Looks up a localized string similar to The desired value is not an acceptable enumerated value..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NotConfigured">
            <summary>
              Looks up a localized string similar to The Adapter has not been configured to a DB context.  This should happen automatically and may be indicative of system issue.  Please call technical support..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NotConnected">
            <summary>
              Looks up a localized string similar to You have not connected this adapter to a specific %EwsServer%.  Did you forget to call Connect?.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.NotificationNotDeletable">
            <summary>
              Looks up a localized string similar to The most most recent Notification session cannot be deleted..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.RequiredField">
            <summary>
              Looks up a localized string similar to {0} is a required field..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ServerNotFoundOrAuthorizationFailed">
            <summary>
              Looks up a localized string similar to The %EwsServer% was not located or the credentials you supplied were incorrect.  Please verify your credentials and try again..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.SubscriptionNotActive">
            <summary>
              Looks up a localized string similar to This operation is only allowed on subscriptions which are still Active..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ValueItemNotForceable">
            <summary>
              Looks up a localized string similar to %ValueItem% cannot be forced..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.EwsServerDataAdapterErrors.ValueItemValueNotConsistentWithType">
            <summary>
              Looks up a localized string similar to The supplied value is not consistent with the %ValueItem%&apos;s Type property..
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.Resources.Prompts">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.Prompts.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.Prompts.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.Prompts.AutoAcknowledgedMessage">
            <summary>
              Looks up a localized string similar to Acknowledged .
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.Prompts.SourceRequired">
            <summary>
              Looks up a localized string similar to Source is required..
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.Resources.Prompts.ValueItemRequired">
            <summary>
              Looks up a localized string similar to ValueItem is required..
            </summary>
        </member>
        <member name="T:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms">
            <summary>
            Models the HTTP Digest hasing algoritmgs by the parent EwsServer instance.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Md5">
            <summary>
            HttpDigestHashAlgorithm.Md5 is supported
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Md5Session">
            <summary>
            HttpDigestHashAlgorithm.Md5 (session variant) is supported
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Sha256">
            <summary>
            HttpDigestHashAlgorithm.Sha256 is supported
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Sha256Session">
            <summary>
            HttpDigestHashAlgorithm.Sha256 (session variant) is supported
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Sha512">
            <summary>
            HttpDigestHashAlgorithm.Sha512-256 is supported
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Sha512Session">
            <summary>
            HttpDigestHashAlgorithm.Sha512-256 (session variant) is supported
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.ToList">
            <summary>
            Returns the supported HttpDigestHashAlgorithm as a list.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.FromList(System.Collections.Generic.List{Ews.Common.Authentication.HttpDigestHashAlgorithm})">
            <summary>
            Creates an instance from the supplied list of enum values.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Data.EwsServerHashingAlgorithms.Equals(System.Object)">
            <inheritdoc />
        </member>
    </members>
</doc>
