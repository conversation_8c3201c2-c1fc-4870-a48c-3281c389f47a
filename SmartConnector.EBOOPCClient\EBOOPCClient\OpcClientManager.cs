﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using OPC;
using OPC.Common;
using OPCDA;
using OPCDA.NET;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x02000008 RID: 8
	public class OpcClientManager
	{
		// Token: 0x06000021 RID: 33 RVA: 0x00003BE4 File Offset: 0x00001DE4
		public int Init()
		{
			this._logFileName = Path.Combine(ServerHelper._OPCClientLogFolder, this._OPCLogicalName + ".log");
			this._ILog = new ILog(this._logFileName, this._logFilter);
			this._ActiveOPCServerIndex = 0;
			this._ILog.Write(ILog.LogLevels.INFO, "OpcClientManager, Init OPC Server '{0}'", new object[]
			{
				this._OPCServerName[this._ActiveOPCServerIndex]
			});
			if (this.IsRedundantOPCServerExist())
			{
				this._ILog.Write(ILog.LogLevels.INFO, "OpcClientManager, Redundant OPC Server '{0}'", new object[]
				{
					this._OPCServerName[1]
				});
			}
			this._OPCThread = null;
			this._Stop = false;
			this._mapHIDToOpcItem = new Dictionary<int, EBOOpcItem>();
			this._mapItemIDToOpcItem = new Dictionary<string, EBOOpcItem>();
			this._mapEWSItemIDToOpcItem = new Dictionary<string, EBOOpcItem>();
			this._lstOPCItems = new List<EBOOpcItem>();
			this._lstOPCItemsOnTheFlyUpdate = new List<EBOOpcItem>();
			return 0;
		}

		// Token: 0x06000022 RID: 34 RVA: 0x00003CC8 File Offset: 0x00001EC8
		public int Start()
		{
			this._ILog.Write(ILog.LogLevels.INFO, "OpcClientManager, Start..");
			this._Stop = false;
			this._OPCThread = new Thread(new ThreadStart(this.OPCThread));
			this._OPCThread.Start();
			return 0;
		}

		// Token: 0x06000023 RID: 35 RVA: 0x00003D06 File Offset: 0x00001F06
		public int Stop()
		{
			if (!this._Stop)
			{
				this._Stop = true;
				this._ILog.Write(ILog.LogLevels.INFO, "OpcClientManager, Stop..");
				if (this._OPCThread != null)
				{
					this._OPCThread.Join();
				}
			}
			return 0;
		}

		// Token: 0x06000024 RID: 36 RVA: 0x00003D40 File Offset: 0x00001F40
		public int AddItem(ref EBOOpcItem opcItem, bool bTrigger)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.DEBUG, "AddItem '{0}'", new object[]
				{
					opcItem._opcItemDef.ItemID
				});
				OPCItemDef[] arrDef = new OPCItemDef[]
				{
					opcItem._opcItemDef
				};
				OPCItemResult[] array;
				bool flag;
				if (bTrigger)
				{
					this._ILog.Write(ILog.LogLevels.DEBUG, "AddItem (1) '{0}'", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					flag = HRESULTS.Succeeded(this._OPCTriggerGrp.AddItems(arrDef, out array));
				}
				else
				{
					flag = HRESULTS.Succeeded(this._OPCGrp.AddItems(arrDef, out array));
				}
				this._ILog.Write(ILog.LogLevels.DEBUG, "AddItem (2) '{0}'", new object[]
				{
					opcItem._opcItemDef.ItemID
				});
				if (!flag)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "AddItem '{0}' fail", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				if (array == null)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "AddItem '{0}' fail, itemResult is NULL", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				if (HRESULTS.Failed(array[0].Error))
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "AddItem '{0}' fail, itemResult = {1:X}", new object[]
					{
						opcItem._opcItemDef.ItemID,
						array[0].Error
					});
					return -1;
				}
				opcItem._opcItemRes.HandleServer = array[0].HandleServer;
				opcItem._opcItemRes.AccessRights = array[0].AccessRights;
				opcItem._opcItemRes.CanonicalDataType = array[0].CanonicalDataType;
				this._ILog.Write(ILog.LogLevels.DEBUG, "AddItem '{0}' pass, itemResult = {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					array[0].Error
				});
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "AddItem error, item '{0}' exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex.Message
				});
				return -1;
			}
			catch (Exception ex2)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "AddItem error, item '{0}' exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex2.Message
				});
				return -1;
			}
			return 0;
		}

		// Token: 0x06000025 RID: 37 RVA: 0x00003FCC File Offset: 0x000021CC
		public int ReadItem(ref EBOOpcItem opcItem)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.DEBUG, "ReadItem {0}", new object[]
				{
					opcItem._opcItemDef.ItemID
				});
				if ((opcItem._opcItemRes.AccessRights & OPCACCESSRIGHTS.OPC_READABLE) == OPCACCESSRIGHTS.OPC_UNKNOWN)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem '{0}' fail, no read access", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				int[] aSrvHnd = new int[]
				{
					opcItem._opcItemRes.HandleServer
				};
				OPCItemState[] array = null;
				if (HRESULTS.Failed(this._OPCGrp.Read(OPCDATASOURCE.OPC_DS_DEVICE, aSrvHnd, out array)))
				{
					if (array == null)
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem '{0}' fail", new object[]
						{
							opcItem._opcItemDef.ItemID
						});
					}
					else
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem '{0}' fail, error = 0x{1}", new object[]
						{
							opcItem._opcItemDef.ItemID,
							array[0].Error.ToString("X8")
						});
					}
					return -1;
				}
				opcItem._opcItemState = array[0];
				if (!HRESULTS.Succeeded(opcItem._opcItemState.Error))
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem '{0}' fail, opcItemState.error = {1}", new object[]
					{
						opcItem._opcItemDef.ItemID,
						opcItem._opcItemState.Error
					});
					return -1;
				}
				if (opcItem._opcItemState.DataValue == null)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem '{0}' fail, opcItemState.DataValue is null", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				object dataValue = opcItem._opcItemState.DataValue;
				if (dataValue.GetType().IsArray)
				{
					Array array2 = dataValue as Array;
					this._ILog.Write(ILog.LogLevels.DEBUG, "ReadItem '{0}'  Array of type '{1}' # of elements {2}", new object[]
					{
						this._lstOPCItems[opcItem._opcItemState.HandleClient]._opcItemDef.ItemID,
						dataValue.GetType().GetElementType(),
						array2.Length
					});
					int num = 0;
					IEnumerator enumerator = array2.GetEnumerator();
					while (enumerator.MoveNext())
					{
						if (enumerator.Current == null)
						{
							break;
						}
						this._ILog.Write(ILog.LogLevels.DEBUG, "[{0}] = {1}", new object[]
						{
							num++,
							enumerator.Current
						});
					}
				}
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem error, item '{0}' exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex.Message
				});
				return -1;
			}
			catch (Exception ex2)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "ReadItem error, item '{0}' exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex2.Message
				});
				return -1;
			}
			return 0;
		}

		// Token: 0x06000026 RID: 38 RVA: 0x000042F0 File Offset: 0x000024F0
		public int WriteItem(ref EBOOpcItem opcItem, object newVal, bool bTriggerGrp = true)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.DEBUG, "WriteItem {0}", new object[]
				{
					opcItem._opcItemDef.ItemID
				});
				if (opcItem._opcItemState.DataValue != null && (opcItem._opcItemRes.AccessRights & OPCACCESSRIGHTS.OPC_WRITEABLE) == OPCACCESSRIGHTS.OPC_UNKNOWN)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem '{0}' fail, no write access", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				int[] arrHSrv = new int[]
				{
					opcItem._opcItemRes.HandleServer
				};
				object[] arrVal = new object[]
				{
					newVal
				};
				int[] array = null;
				if (bTriggerGrp)
				{
					if (HRESULTS.Failed(this._OPCTriggerGrp.Write(arrHSrv, arrVal, out array)))
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem '{0}' fail", new object[]
						{
							opcItem._opcItemDef.ItemID
						});
						return -1;
					}
				}
				else if (this._OPCGrp.Write(arrHSrv, arrVal, out array) > 0)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem '{0}' fail", new object[]
					{
						opcItem._opcItemDef.ItemID
					});
					return -1;
				}
				try
				{
					if (!HRESULTS.Succeeded(array[0]))
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem '{0}' fail, item error = {1}", new object[]
						{
							opcItem._opcItemDef.ItemID,
							array[0]
						});
						return -1;
					}
					opcItem._opcItemState.DataValue = newVal;
				}
				catch (Exception)
				{
					opcItem._opcItemState.DataValue = newVal;
				}
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem error, item '{0}' COM exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex.Message
				});
				return -1;
			}
			catch (Exception ex2)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "WriteItem error, item '{0}' exception {1}", new object[]
				{
					opcItem._opcItemDef.ItemID,
					ex2.Message
				});
				return -1;
			}
			return 0;
		}

		// Token: 0x06000027 RID: 39 RVA: 0x00004540 File Offset: 0x00002740
		private void OPCThread()
		{
			Thread.CurrentThread.Name = "OPC";
			this._ILog.Write(ILog.LogLevels.INFO, "OPC Thread Start...");
			bool flag = false;
			int num = 0;
			this._ServerHasShutdown = false;
			this._Running = false;
			while (!this._Stop && !ServerHelper._UpdateProcessorStop)
			{
				Thread.Sleep(1000);
				if (num == 0 || Environment.TickCount - num >= 60000)
				{
					num = Environment.TickCount;
					if (flag)
					{
						this.Shutdown();
					}
					if (!this.Connect())
					{
						flag = true;
						if (this.IsRedundantOPCServerExist())
						{
							this._ActiveOPCServerIndex = 1 - this._ActiveOPCServerIndex;
						}
					}
					else if (!this._Stop && !this.CreateGroups())
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "CreateOPCGroups fail, can't continue");
						num = Environment.TickCount;
						flag = true;
					}
					else if (!this._Stop && !this.AddAllItems())
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "AddAllItems fail, can't continue");
						num = Environment.TickCount;
						flag = true;
					}
					else
					{
						this._Running = true;
						this._ILog.Write(ILog.LogLevels.INFO, "start update loop ...");
						try
						{
							while (!this._Stop && this._OPCServer != null && (!(this._OPCServerName[this._ActiveOPCServerIndex].Substring(0, 3) != "ua:") || this._OPCServer.isConnectedDA) && !this._ServerHasShutdown && !ServerHelper._UpdateProcessorStop)
							{
								if (this._lstOPCItemsOnTheFlyUpdate.Count > 0)
								{
									if (!this.AddItemsOnTheFly())
									{
										this._ILog.Write(ILog.LogLevels.ERROR, "AddItemsOnTheFly fail!!!");
									}
									this._lstOPCItemsOnTheFlyUpdate.Clear();
								}
								Thread.Sleep(10);
							}
						}
						catch (Exception ex)
						{
							this._ILog.Write(ILog.LogLevels.ERROR, "update loop fail, error '{0}'", new object[]
							{
								ex.Message
							});
						}
						this._ILog.Write(ILog.LogLevels.INFO, "update loop ended");
						if (ServerHelper._UpdateProcessorStop || this._Stop)
						{
							break;
						}
						this._Running = false;
						flag = false;
						this._ServerHasShutdown = false;
						this.Shutdown();
						this._NeedToSetOFFLine = true;
						num = Environment.TickCount;
					}
				}
			}
			this._ILog.Write(ILog.LogLevels.INFO, "OPC Thread end main loop, Stop: {0}, UpdateProcessorStop: {1} ", new object[]
			{
				this._Stop,
				ServerHelper._UpdateProcessorStop
			});
			this.Shutdown();
			this._ILog.Write(ILog.LogLevels.INFO, "OPC Thread End");
		}

		// Token: 0x06000028 RID: 40 RVA: 0x000047B8 File Offset: 0x000029B8
		private bool Connect()
		{
			try
			{
				string text = "";
				if (this.IsRedundantOPCServerExist() && this._ActiveOPCServerIndex == 1)
				{
					text = "Redundant ";
				}
				this._ILog.Write(ILog.LogLevels.INFO, "Connecting to {0}OPC server: '{1}'", new object[]
				{
					text,
					this._OPCServerName[this._ActiveOPCServerIndex]
				});
				this._OPCServer = new OpcServer();
				this._OPCServer.UaAccessWithoutSecurityPreferred = InitApp._UaAccessWithoutSecurityPreferred;
				this._OPCServer.CertificateDomainMustMatch = InitApp._CertificateDomainMustMatch;
				if (!string.IsNullOrEmpty(this._OPCUAUsername) && !string.IsNullOrEmpty(this._OPCUAPassword))
				{
					this._ILog.Write(ILog.LogLevels.DEBUG, "Connecting to UA OPC using user: '{0}', password: '{1}' ...'", new object[]
					{
						this._OPCUAUsername,
						this._OPCUAPassword
					});
					Host host = new Host();
					host.UserName = this._OPCUAUsername;
					host.Password = this._OPCUAPassword;
					int num;
					if (HRESULTS.Failed(num = this._OPCServer.Connect(host, this._OPCServerName[this._ActiveOPCServerIndex])))
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "connect '{0}', user: '{1}', password: '{2}' fail, hResult {3:X}", new object[]
						{
							this._OPCServerName[this._ActiveOPCServerIndex],
							this._OPCUAUsername,
							this._OPCUAPassword,
							num
						});
						return false;
					}
				}
				else
				{
					this._ILog.Write(ILog.LogLevels.DEBUG, "Connecting to {0}OPC Server...'", new object[]
					{
						text
					});
					int num;
					if (HRESULTS.Failed(num = this._OPCServer.Connect(this._OPCServerName[this._ActiveOPCServerIndex])))
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "connect to {0}OPC Server '{1}' fail, hResult {2:X}", new object[]
						{
							text,
							this._OPCServerName[this._ActiveOPCServerIndex],
							num
						});
						return false;
					}
				}
				this._ILog.Write(ILog.LogLevels.DEBUG, "Connecting to OPC Server (after connect)'");
				Thread.Sleep(100);
				this._OPCServer.SetClientName(this._OPCServerName[this._ActiveOPCServerIndex]);
				this._ILog.Write(ILog.LogLevels.DEBUG, "Connecting to OPC Server (after set client)'");
				SERVERSTATUS serverstatus;
				this._OPCServer.GetStatus(out serverstatus);
				this._ILog.Write(ILog.LogLevels.DEBUG, "Connecting to OPC Server (after get status)'");
				StringBuilder stringBuilder = new StringBuilder(serverstatus.szVendorInfo, 200);
				stringBuilder.AppendFormat(" {0}.{1}.{2}", serverstatus.wMajorVersion, serverstatus.wMinorVersion, serverstatus.wBuildNumber);
				this._OPCServer.ShutdownRequested += this.opcSrv_ServerShutDown;
				this._ILog.Write(ILog.LogLevels.INFO, "Connected pass, {0}OPC Server version: {1}", new object[]
				{
					text,
					stringBuilder
				});
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "connect error, exception {0}", new object[]
				{
					ex.Message
				});
				return false;
			}
			catch (Exception ex2)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "connect error, exception {0}", new object[]
				{
					ex2.Message
				});
				return false;
			}
			return true;
		}

		// Token: 0x06000029 RID: 41 RVA: 0x00004AF4 File Offset: 0x00002CF4
		private int Shutdown()
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "Shutdown OPC...");
				if (this._OPCTriggerGrp != null)
				{
					this._ILog.Write(ILog.LogLevels.INFO, "Remove OPC group [1]...");
					this._OPCTriggerGrp.DataChanged -= this.TriggerGrp_DataChange;
					this._OPCTriggerGrp.Remove(false);
					this._OPCTriggerGrp = null;
				}
				if (this._OPCGrp != null)
				{
					this._ILog.Write(ILog.LogLevels.INFO, "Remove OPC group [2]...");
					this._OPCGrp.Remove(false);
					this._OPCGrp = null;
				}
				if (this._OPCServer != null)
				{
					this._ILog.Write(ILog.LogLevels.INFO, "disconnect OPC server...");
					this._OPCServer.Disconnect();
					this._OPCServer = null;
					this._ILog.Write(ILog.LogLevels.INFO, "OPC server disconnected...");
				}
				this._ILog.Write(ILog.LogLevels.INFO, "Shutdown OPC done");
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "Shutdown OPC error, exception {0}", new object[]
				{
					ex.Message
				});
				return -1;
			}
			return 0;
		}

		// Token: 0x0600002A RID: 42 RVA: 0x00004C14 File Offset: 0x00002E14
		private bool CreateGroups()
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "Create OPC groups...");
				int num = 1;
				int hresultcode;
				this._OPCTriggerGrp = this._OPCServer.AddGroup("", true, this._OPCGroupRate, num, out hresultcode);
				if (HRESULTS.Failed(hresultcode))
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "create opc groups {0} error", new object[]
					{
						num
					});
					return false;
				}
				this._OPCTriggerGrp.DataChanged += this.TriggerGrp_DataChange;
				this._OPCTriggerGrp.WriteCompleted += this.TriggerGrp_WriteComplete;
				this._OPCTriggerGrp.AdviseIOPCDataCallback();
				num = 2;
				this._OPCGrp = this._OPCServer.AddGroup("GrpMain", false, 60000, num, out hresultcode);
				if (HRESULTS.Failed(hresultcode))
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "create opc groups {0} error", new object[]
					{
						num
					});
					return false;
				}
				this._ILog.Write(ILog.LogLevels.INFO, "Create OPC groups done OK");
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "create opc groups error, exception {0}", new object[]
				{
					ex.Message
				});
				return false;
			}
			return true;
		}

		// Token: 0x0600002B RID: 43 RVA: 0x00004D5C File Offset: 0x00002F5C
		private bool AddAllItems()
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "AddAllItems...");
				int num = 0;
				int num2 = 0;
				for (int i = 0; i < this._lstOPCItems.Count; i++)
				{
					EBOOpcItem eboopcItem = this._lstOPCItems[i];
					if (this.AddItem(ref eboopcItem, true) != 0)
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "AddItem '{0}' error", new object[]
						{
							eboopcItem._opcItemDef.ItemID
						});
						num++;
					}
					else
					{
						num2++;
					}
				}
				this._ILog.Write(ILog.LogLevels.INFO, "AddAllItems ended, # of items added: {0}, # of errors: {1}, total: {2}", new object[]
				{
					num2,
					num,
					this._lstOPCItems.Count
				});
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "AddAllItems error, exception {0}", new object[]
				{
					ex.Message
				});
				return false;
			}
			return true;
		}

		// Token: 0x0600002C RID: 44 RVA: 0x00004E5C File Offset: 0x0000305C
		private bool AddItemsOnTheFly()
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "AddItemsOnTheFly...");
				int num = 0;
				int num2 = 0;
				for (int i = 0; i < this._lstOPCItemsOnTheFlyUpdate.Count; i++)
				{
					EBOOpcItem eboopcItem = this._lstOPCItemsOnTheFlyUpdate[i];
					if (this.AddItem(ref eboopcItem, true) != 0)
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "AddItem '{0}' error", new object[]
						{
							eboopcItem._opcItemDef.ItemID
						});
						num++;
					}
					else
					{
						num2++;
					}
				}
				this._ILog.Write(ILog.LogLevels.INFO, "AddItemsOnTheFly ended, # of items added: {0}, # of errors: {1}, total: {2}", new object[]
				{
					num2,
					num,
					this._lstOPCItemsOnTheFlyUpdate.Count
				});
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "AddItemsOnTheFly error, exception {0}", new object[]
				{
					ex.Message
				});
				return false;
			}
			return true;
		}

		// Token: 0x0600002D RID: 45 RVA: 0x00004F5C File Offset: 0x0000315C
		private bool RemoveItems(ref OpcGroup opcGroup, int nItems)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "Remove OPC Items from group {0}", new object[]
				{
					opcGroup.Name
				});
				int[] arrHSrv = new int[nItems];
				int[] array;
				opcGroup.RemoveItems(arrHSrv, out array);
			}
			catch (COMException ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "Remove OPC Items error, exception {0}", new object[]
				{
					ex.Message
				});
				return false;
			}
			return true;
		}

		// Token: 0x0600002E RID: 46 RVA: 0x00004FD8 File Offset: 0x000031D8
		public TypeCode VT2TypeCode(VarEnum vevt)
		{
			switch (vevt)
			{
			case VarEnum.VT_I2:
				return TypeCode.Int16;
			case VarEnum.VT_I4:
				return TypeCode.Int32;
			case VarEnum.VT_R4:
				return TypeCode.Single;
			case VarEnum.VT_R8:
				return TypeCode.Double;
			case VarEnum.VT_CY:
				return TypeCode.Double;
			case VarEnum.VT_DATE:
				return TypeCode.DateTime;
			case VarEnum.VT_BSTR:
				return TypeCode.String;
			case VarEnum.VT_BOOL:
				return TypeCode.Boolean;
			case VarEnum.VT_DECIMAL:
				return TypeCode.Decimal;
			case VarEnum.VT_I1:
				return TypeCode.SByte;
			case VarEnum.VT_UI1:
				return TypeCode.Byte;
			case VarEnum.VT_UI2:
				return TypeCode.UInt16;
			case VarEnum.VT_UI4:
				return TypeCode.UInt32;
			case VarEnum.VT_I8:
				return TypeCode.Int64;
			case VarEnum.VT_UI8:
				return TypeCode.UInt64;
			}
			return TypeCode.Object;
		}

		// Token: 0x0600002F RID: 47 RVA: 0x00005068 File Offset: 0x00003268
		private bool IsRedundantOPCServerExist()
		{
			return !string.IsNullOrEmpty(this._OPCServerName[1]);
		}

		// Token: 0x06000030 RID: 48 RVA: 0x0000507A File Offset: 0x0000327A
		protected void opcSrv_ServerShutDown(object sender, ShutdownRequestEventArgs e)
		{
			this._ILog.Write(ILog.LogLevels.INFO, "OPC server shut down because: " + e.shutdownReason);
			this._ServerHasShutdown = true;
		}

		// Token: 0x06000031 RID: 49 RVA: 0x000050A0 File Offset: 0x000032A0
		protected void TriggerGrp_DataChange(object sender, DataChangeEventArgs e)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.DEBUG, "TriggerGrp_DataChange: transaction id = {0}, master error = 0x{1}", new object[]
				{
					e.transactionID.ToString(),
					e.masterError.ToString("X")
				});
				int num = e.sts.Length;
				int num2 = 0;
				foreach (OPCItemState opcitemState in e.sts)
				{
					if (HRESULTS.Succeeded(opcitemState.Error))
					{
						if (opcitemState.DataValue != null)
						{
							this._ILog.Write(ILog.LogLevels.DEBUG, "TriggerGrp_DataChange: item client handle = {0}, item ID = '{1}', EWS ID = '{2}'", new object[]
							{
								opcitemState.HandleClient,
								this._lstOPCItems[opcitemState.HandleClient]._opcItemDef.ItemID,
								this._lstOPCItems[opcitemState.HandleClient]._EWSItemID
							});
							this._lstOPCItems[opcitemState.HandleClient]._opcItemState.DataValue = opcitemState.DataValue;
							this._lstOPCItems[opcitemState.HandleClient]._opcItemState.TimeStamp = opcitemState.TimeStamp;
							this._lstOPCItems[opcitemState.HandleClient]._opcItemState.Quality = opcitemState.Quality;
							this._lstOPCItems[opcitemState.HandleClient]._HasChange = true;
							this._ILog.Write(ILog.LogLevels.DEBUG, "TriggerGrp_DataChange: item client handle = {0}, item ID = '{1}', Value = '{2}', Quality = 0x{3:X}", new object[]
							{
								opcitemState.HandleClient,
								this._lstOPCItems[opcitemState.HandleClient]._opcItemDef.ItemID,
								this._lstOPCItems[opcitemState.HandleClient]._opcItemState.DataValue.ToString(),
								this._lstOPCItems[opcitemState.HandleClient]._opcItemState.Quality
							});
							if (this._lstOPCItems[opcitemState.HandleClient]._opcItemState.Quality == 24)
							{
								num2++;
							}
							object dataValue = opcitemState.DataValue;
							if (dataValue.GetType().IsArray)
							{
								Array array = dataValue as Array;
								this._ILog.Write(ILog.LogLevels.DEBUG, "TriggerGrp_DataChange: item client handle = {0}, item ID = '{1}' is Array of type '{2}' # of elements {3}", new object[]
								{
									opcitemState.HandleClient,
									this._lstOPCItems[opcitemState.HandleClient]._opcItemDef.ItemID,
									dataValue.GetType().GetElementType(),
									array.Length
								});
								int num3 = 0;
								IEnumerator enumerator = array.GetEnumerator();
								while (enumerator.MoveNext())
								{
									if (enumerator.Current == null)
									{
										break;
									}
									this._ILog.Write(ILog.LogLevels.DEBUG, "[{0}] = {1}", new object[]
									{
										num3++,
										enumerator.Current
									});
								}
							}
						}
					}
					else
					{
						this._ILog.Write(ILog.LogLevels.ERROR, "TriggerGrp_DataChange: itemID '{0}', item client id = {1}, error = 0x{2}", new object[]
						{
							this._lstOPCItems[opcitemState.HandleClient]._opcItemDef.ItemID,
							opcitemState.HandleClient,
							opcitemState.Error.ToString("X")
						});
					}
				}
				this._NeedUpdateEWS = true;
				if (this.IsRedundantOPCServerExist() && num2 == num)
				{
					this._ILog.Write(ILog.LogLevels.ERROR, "TriggerGrp_DataChange: OPC server '{0}' has Comm failure! Disconnect from OPC server", new object[]
					{
						this._OPCServerName[this._ActiveOPCServerIndex]
					});
					this._ActiveOPCServerIndex = 1 - this._ActiveOPCServerIndex;
					this._ServerHasShutdown = true;
				}
			}
			catch (Exception ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "TriggerGrp_DataChange error, Exception {0}", new object[]
				{
					ex.Message
				});
			}
			this._ILog.Write(ILog.LogLevels.DEBUG, "TriggerGrp_DataChange: transaction id = {0} done", new object[]
			{
				e.transactionID.ToString()
			});
		}

		// Token: 0x06000032 RID: 50 RVA: 0x000054B0 File Offset: 0x000036B0
		protected void TriggerGrp_WriteComplete(object sender, WriteCompleteEventArgs e)
		{
			try
			{
				this._ILog.Write(ILog.LogLevels.INFO, "TriggerGrp_WriteComplete: transaction id = {0}, master error = 0x{1}" + e.transactionID.ToString(), new object[]
				{
					e.masterError.ToString("X")
				});
				HRESULTS.Failed(e.res[0].Error);
			}
			catch (Exception ex)
			{
				this._ILog.Write(ILog.LogLevels.ERROR, "TriggerGrp_WriteComplete error, Exception {0}", new object[]
				{
					ex.Message
				});
			}
		}

		// Token: 0x0400001D RID: 29
		public const string OPCStatusItemName = "OPC.Status";

		// Token: 0x0400001E RID: 30
		public int _OPCServerIndex = -1;

		// Token: 0x0400001F RID: 31
		public string _OPCLogicalName = string.Empty;

		// Token: 0x04000020 RID: 32
		public int _ActiveOPCServerIndex;

		// Token: 0x04000021 RID: 33
		public string[] _OPCServerName = new string[]
		{
			"",
			""
		};

		// Token: 0x04000022 RID: 34
		public string _OPCNode = string.Empty;

		// Token: 0x04000023 RID: 35
		public string _OPCClassID = string.Empty;

		// Token: 0x04000024 RID: 36
		public string _OPCUAUsername = string.Empty;

		// Token: 0x04000025 RID: 37
		public string _OPCUAPassword = string.Empty;

		// Token: 0x04000026 RID: 38
		public int _OPCGroupRate = 1000;

		// Token: 0x04000027 RID: 39
		public int _OPCInitTimeout = 60000;

		// Token: 0x04000028 RID: 40
		public bool _OPCheckLastUpdateTime;

		// Token: 0x04000029 RID: 41
		public bool _OPCForceReadAfterAddItem;

		// Token: 0x0400002A RID: 42
		public bool _OPCAddItemsCount;

		// Token: 0x0400002B RID: 43
		public EBOOpcItem _RedundencyActive;

		// Token: 0x0400002C RID: 44
		public EBOOpcItem _WD;

		// Token: 0x0400002D RID: 45
		public EBOOpcItem _Shutdown;

		// Token: 0x0400002E RID: 46
		public Dictionary<int, EBOOpcItem> _mapHIDToOpcItem;

		// Token: 0x0400002F RID: 47
		public Dictionary<string, EBOOpcItem> _mapItemIDToOpcItem;

		// Token: 0x04000030 RID: 48
		public Dictionary<string, EBOOpcItem> _mapEWSItemIDToOpcItem;

		// Token: 0x04000031 RID: 49
		public List<EBOOpcItem> _lstOPCItems;

		// Token: 0x04000032 RID: 50
		public List<EBOOpcItem> _lstOPCItemsOnTheFlyUpdate;

		// Token: 0x04000033 RID: 51
		public static int _TotalItems;

		// Token: 0x04000034 RID: 52
		public OpcServer _OPCServer;

		// Token: 0x04000035 RID: 53
		public OpcGroup _OPCTriggerGrp;

		// Token: 0x04000036 RID: 54
		public OpcGroup _OPCGrp;

		// Token: 0x04000037 RID: 55
		public bool _NeedUpdateEWS;

		// Token: 0x04000038 RID: 56
		private Thread _OPCThread;

		// Token: 0x04000039 RID: 57
		private bool _Stop;

		// Token: 0x0400003A RID: 58
		public bool _Running;

		// Token: 0x0400003B RID: 59
		public bool _ServerHasShutdown;

		// Token: 0x0400003C RID: 60
		public bool _NeedToSetOFFLine;

		// Token: 0x0400003D RID: 61
		public bool _ServerHasCommFailure;

		// Token: 0x0400003E RID: 62
		public ILog.LogLevels _logFilter = ILog.LogLevels.INFO;

		// Token: 0x0400003F RID: 63
		private string _logFileName = string.Empty;

		// Token: 0x04000040 RID: 64
		private ILog _ILog;
	}
}
