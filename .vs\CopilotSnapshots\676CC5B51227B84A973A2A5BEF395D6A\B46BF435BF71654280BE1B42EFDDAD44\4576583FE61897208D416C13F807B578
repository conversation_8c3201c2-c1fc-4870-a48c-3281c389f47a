﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using Ews.Common;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Mongoose.Process;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x0200000B RID: 11
	[ConfigurationDefaults("EBO-OPC Client Update Processor", "EBO-OPC client Updates (subscription mode)")]
	public class UpdateProcessor : EBOOpcClientProcessorBase, ILongRunningProcess
	{
		// Token: 0x06000039 RID: 57 RVA: 0x000058D8 File Offset: 0x00003AD8
		protected override IEnumerable<Prompt> Execute_Subclass()
		{
			EBOOpcClientProcessorBase._IsStart = true;
			ServerHelper._UpdateProcessorStop = false;
			InitApp.StartApplicationSetLogFile();
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, wait until setup processor ended... ");
			try
			{
				do
				{
					Thread.Sleep(10);
					if (ServerHelper._SetupProcessorRunandEnded)
					{
						break;
					}
					base.CheckCancellationToken();
				}
				while (!base.IsCancellationRequested);
				InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, setup processor ended");
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateProcessor:Execute_Subclass, Long loop error '{0}'", new object[]
				{
					ex.Message
				});
			}
			ServerHelper._UpdateProcessor = this;
			if ((EBOOpcClientProcessorBase._StartStatus = InitApp.StartApplication()) != 0)
			{
				return new List<Prompt>
				{
					base.InitFailPrompt()
				};
			}
			if (!base.IsConnected)
			{
				return new List<Prompt>
				{
					base.CreateCannotConnectPrompt()
				};
			}
			DateTime now = DateTime.Now;
			DateTime now2 = DateTime.Now;
			TimeSpan timeSpan = default(TimeSpan);
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, start long loop...");
			try
			{
				do
				{
					Thread.Sleep(10);
					base.CheckCancellationToken();
					if ((DateTime.Now - now2).TotalSeconds > 3.0)
					{
						now2 = DateTime.Now;
						this.ManageAddItemsOnTheFly();
					}
					this.DoUpdatesOPCItems();
					for (int i = 0; i < ServerHelper._lstOpcClientManager.Count; i++)
					{
						base.CheckCancellationToken();
						OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[i];
						if (opcClientManager._NeedToSetOFFLine)
						{
							ServerHelper._UpdateProcessor.SetOPCItemsToOffline(i, 3);
							opcClientManager._NeedToSetOFFLine = false;
						}
					}
					if ((DateTime.Now - now).TotalSeconds > 60.0)
					{
						if ((DateTime.Now - this._lastUpdateEWSItems).TotalSeconds > 2.0)
						{
							now = DateTime.Now;
							this.DeleteSubscriptionNotifications();
						}
						else
						{
							InitApp._ILog.Write(ILog.LogLevels.INFO, "DeleteSubscriptionNotifications suspend because in update EWS items");
						}
					}
				}
				while (!base.IsCancellationRequested);
			}
			catch (Exception ex2)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateProcessor:Execute_Subclass, Long loop error '{0}'", new object[]
				{
					ex2.Message
				});
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, Long loop ended");
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, stoping OPC servers...");
			ServerHelper._UpdateProcessorStop = true;
			Thread.Sleep(500);
			this.StopAllOPCServers();
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, opc servers stoped");
			try
			{
				InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, set all items state to OFF-LINE...");
				this.SetOPCItemsToOffline(3);
				InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateProcessor:Execute_Subclass, set all items state to OFF-LINE done");
			}
			catch (Exception ex3)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateProcessor:Execute_Subclass, set all items state to OFF-LINE fail '{0}'", new object[]
				{
					ex3.Message
				});
			}
			return new List<Prompt>();
		}

		// Token: 0x0600003A RID: 58 RVA: 0x00005BA4 File Offset: 0x00003DA4
		private bool DoUpdatesOPCItems()
		{
			this.hasUpdateEWSItems = false;
			InitApp._ILog.Write(ILog.LogLevels.DEBUG, "DoUpdatesOPCItems...");
			List<string> list = (from a in base.DataAdapter.SubscriptionItems
			where (int)a.Subscription.Status == 1
			select a.AlternateId).Distinct<string>().ToList<string>();
			InitApp._ILog.Write(ILog.LogLevels.DEBUG, "DoUpdatesOPCItems, subscribedItems #: {0}", new object[]
			{
				list.Count
			});
			if (list.Count <= 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.DEBUG, "DoUpdatesOPCItems, subscribedItems is empty!");
				return true;
			}
			for (int i = 0; i < ServerHelper._lstOpcClientManager.Count; i++)
			{
				base.CheckCancellationToken();
				OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[i];
				base.DataAdapter.AutoCommit = false;
				opcClientManager._NeedUpdateEWS = false;
				int num;
				if (opcClientManager._OPCServer == null)
				{
					num = -1;
				}
				else if (opcClientManager._OPCServerName[opcClientManager._ActiveOPCServerIndex].Substring(0, 3) != "ua:")
				{
					num = (opcClientManager._OPCServer.isConnectedDA ? 0 : -1);
				}
				else
				{
					num = 0;
				}
				string text = string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, "OPC.Status");
				if (list.Contains(text))
				{
					base.DataAdapter.ModifyValueItemValue(text, new int?(num), new EwsValueStateEnum?(0));
				}
				if (num == 0)
				{
					for (int j = 0; j < opcClientManager._lstOPCItems.Count; j++)
					{
						base.CheckCancellationToken();
						EBOOpcItem eboopcItem = opcClientManager._lstOPCItems[j];
						if (eboopcItem._HasChange)
						{
							text = string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, eboopcItem._EWSItemID);
							if (list.Contains(text) || eboopcItem._NumOfItemsInArray > 1)
							{
								try
								{
									this.UpdateEWSItemValue(text, ref eboopcItem);
									eboopcItem._HasChange = false;
									this.hasUpdateEWSItems = true;
									this._lastUpdateEWSItems = DateTime.Now;
								}
								catch (Exception ex)
								{
									InitApp._ILog.Write(ILog.LogLevels.ERROR, "DoUpdatesOPCItems fail on item '{0}', type {1}, error '{2}'", new object[]
									{
										text,
										eboopcItem._DataTypeFromUCME,
										ex.Message
									});
								}
							}
						}
					}
					base.DataAdapter.CommitChanges();
					base.DataAdapter.AutoCommit = true;
				}
			}
			return true;
		}

		// Token: 0x0600003B RID: 59 RVA: 0x00005EA0 File Offset: 0x000040A0
		private bool UpdateEWSItemValue(string EWSItemID, ref EBOOpcItem opcItem)
		{
			EwsValueStateEnum ewsValueStateEnum = this.CvtOPCQualityToEWSQuality(opcItem._opcItemState.Quality);
			string text = opcItem._opcItemState.DataValue.ToString();
			InitApp._ILog.Write(ILog.LogLevels.DEBUG, "Update EWS item: '{0}', val: '{1}', Quality: '{2}'", new object[]
			{
				EWSItemID,
				text,
				ewsValueStateEnum
			});
			object dataValue = opcItem._opcItemState.DataValue;
			if (!dataValue.GetType().IsArray)
			{
				this.DoUpdateEWSItemValue(EWSItemID, opcItem._DataTypeFromUCME, ewsValueStateEnum, ref dataValue);
			}
			else
			{
				Array array = dataValue as Array;
				InitApp._ILog.Write(ILog.LogLevels.DEBUG, "Update EWS item array: '{0}', val: '{1}', Quality: '{2}', # of items: {3}", new object[]
				{
					EWSItemID,
					text,
					ewsValueStateEnum,
					array.Length
				});
				int num = 1;
				IEnumerator enumerator = array.GetEnumerator();
				while (enumerator.MoveNext() && enumerator.Current != null && num <= opcItem._NumOfItemsInArray)
				{
					object obj = enumerator.Current;
					string text2 = EWSItemID + "#[" + num.ToString() + "]";
					num++;
					InitApp._ILog.Write(ILog.LogLevels.DEBUG, "{0} = {1}", new object[]
					{
						text2,
						enumerator.Current
					});
					this.DoUpdateEWSItemValue(text2, opcItem._DataTypeFromUCME, ewsValueStateEnum, ref obj);
				}
			}
			return true;
		}

		// Token: 0x0600003C RID: 60 RVA: 0x00005FF4 File Offset: 0x000041F4
		private bool DoUpdateEWSItemValue(string EWSItemID, string dataTypeFromUCME, EwsValueStateEnum ewsQuality, ref object itemVal)
		{
			string text = itemVal.ToString();
			uint num = <PrivateImplementationDetails>.ComputeStringHash(dataTypeFromUCME);
			if (num > 1683620383U)
			{
				if (num <= 2797886853U)
				{
					if (num != 2579740217U)
					{
						if (num != 2699759368U)
						{
							if (num != 2797886853U)
							{
								goto IL_2C8;
							}
							if (!(dataTypeFromUCME == "float"))
							{
								goto IL_2C8;
							}
							float num2 = float.Parse(text);
							base.DataAdapter.ModifyValueItemValue(EWSItemID, new double?((double)num2), new EwsValueStateEnum?(ewsQuality));
							return true;
						}
						else
						{
							if (!(dataTypeFromUCME == "double"))
							{
								goto IL_2C8;
							}
							base.DataAdapter.ModifyValueItemValue(EWSItemID, new double?((double)itemVal), new EwsValueStateEnum?(ewsQuality));
							return true;
						}
					}
					else if (!(dataTypeFromUCME == "UI64"))
					{
						goto IL_2C8;
					}
				}
				else if (num <= 2837873542U)
				{
					if (num != 2823553821U)
					{
						if (num != 2837873542U)
						{
							goto IL_2C8;
						}
						if (!(dataTypeFromUCME == "I64"))
						{
							goto IL_2C8;
						}
					}
					else
					{
						if (!(dataTypeFromUCME == "char"))
						{
							goto IL_2C8;
						}
						goto IL_21A;
					}
				}
				else if (num != 3365180733U)
				{
					if (num != 3437915536U)
					{
						goto IL_2C8;
					}
					if (!(dataTypeFromUCME == "datetime"))
					{
						goto IL_2C8;
					}
					base.DataAdapter.ModifyValueItemValue(EWSItemID, new DateTime?((DateTime)itemVal), new EwsValueStateEnum?(ewsQuality));
					return true;
				}
				else
				{
					if (!(dataTypeFromUCME == "bool"))
					{
						goto IL_2C8;
					}
					base.DataAdapter.ModifyValueItemValue(EWSItemID, new bool?((bool)itemVal), new EwsValueStateEnum?(ewsQuality));
					return true;
				}
				long value = long.Parse(text);
				base.DataAdapter.ModifyValueItemValue(EWSItemID, new long?(value), new EwsValueStateEnum?(ewsQuality));
				return true;
			}
			if (num <= 633095128U)
			{
				if (num != 398550328U)
				{
					if (num != 565690462U)
					{
						if (num != 633095128U)
						{
							goto IL_2C8;
						}
						if (!(dataTypeFromUCME == "UI16"))
						{
							goto IL_2C8;
						}
					}
					else if (!(dataTypeFromUCME == "UI32"))
					{
						goto IL_2C8;
					}
				}
				else
				{
					if (!(dataTypeFromUCME == "string"))
					{
						goto IL_2C8;
					}
					goto IL_2C8;
				}
			}
			else if (num != 758287429U)
			{
				if (num != 959324667U)
				{
					if (num != 1683620383U)
					{
						goto IL_2C8;
					}
					if (!(dataTypeFromUCME == "byte"))
					{
						goto IL_2C8;
					}
				}
				else if (!(dataTypeFromUCME == "I16"))
				{
					goto IL_2C8;
				}
			}
			else if (!(dataTypeFromUCME == "I32"))
			{
				goto IL_2C8;
			}
			IL_21A:
			int value2 = int.Parse(text);
			base.DataAdapter.ModifyValueItemValue(EWSItemID, new int?(value2), new EwsValueStateEnum?(ewsQuality));
			return true;
			IL_2C8:
			base.DataAdapter.ModifyValueItemValue(EWSItemID, text, new EwsValueStateEnum?(ewsQuality));
			return true;
		}

		// Token: 0x0600003D RID: 61 RVA: 0x000062E0 File Offset: 0x000044E0
		public static EwsValueTypeEnum ConvertOPCDateValueToEWSValue(string opcDataType)
		{
			uint num = <PrivateImplementationDetails>.ComputeStringHash(opcDataType);
			if (num > 1683620383U)
			{
				if (num <= 2797886853U)
				{
					if (num != 2579740217U)
					{
						if (num != 2699759368U)
						{
							if (num != 2797886853U)
							{
								return 2;
							}
							if (!(opcDataType == "float"))
							{
								return 2;
							}
						}
						else if (!(opcDataType == "double"))
						{
							return 2;
						}
						return 3;
					}
					if (!(opcDataType == "UI64"))
					{
						return 2;
					}
				}
				else if (num <= 2837873542U)
				{
					if (num != 2823553821U)
					{
						if (num != 2837873542U)
						{
							return 2;
						}
						if (!(opcDataType == "I64"))
						{
							return 2;
						}
					}
					else
					{
						if (!(opcDataType == "char"))
						{
							return 2;
						}
						return 5;
					}
				}
				else if (num != 3365180733U)
				{
					if (num != 3437915536U)
					{
						return 2;
					}
					if (!(opcDataType == "datetime"))
					{
						return 2;
					}
					return 0;
				}
				else
				{
					if (!(opcDataType == "bool"))
					{
						return 2;
					}
					return 1;
				}
				return 4;
			}
			if (num <= 633095128U)
			{
				if (num != 398550328U)
				{
					if (num != 565690462U)
					{
						if (num != 633095128U)
						{
							return 2;
						}
						if (!(opcDataType == "UI16"))
						{
							return 2;
						}
					}
					else if (!(opcDataType == "UI32"))
					{
						return 2;
					}
				}
				else
				{
					if (!(opcDataType == "string"))
					{
						return 2;
					}
					return 2;
				}
			}
			else if (num != 758287429U)
			{
				if (num != 959324667U)
				{
					if (num != 1683620383U)
					{
						return 2;
					}
					if (!(opcDataType == "byte"))
					{
						return 2;
					}
				}
				else if (!(opcDataType == "I16"))
				{
					return 2;
				}
			}
			else if (!(opcDataType == "I32"))
			{
				return 2;
			}
			return 5;
		}

		// Token: 0x0600003E RID: 62 RVA: 0x000064A4 File Offset: 0x000046A4
		private EwsValueStateEnum CvtOPCQualityToEWSQuality(short opcQuality)
		{
			if (opcQuality <= 24)
			{
				if (opcQuality <= 8)
				{
					if (opcQuality == 0 || opcQuality == 4)
					{
						return 4;
					}
					if (opcQuality != 8)
					{
						return 4;
					}
				}
				else if (opcQuality <= 16)
				{
					if (opcQuality != 12 && opcQuality != 16)
					{
						return 4;
					}
					return 4;
				}
				else
				{
					if (opcQuality == 20)
					{
						return 4;
					}
					if (opcQuality != 24)
					{
						return 4;
					}
				}
				return 3;
			}
			if (opcQuality <= 80)
			{
				if (opcQuality <= 64)
				{
					if (opcQuality != 28)
					{
						if (opcQuality != 64)
						{
							return 4;
						}
						return 1;
					}
				}
				else if (opcQuality != 68 && opcQuality != 80)
				{
					return 4;
				}
			}
			else if (opcQuality <= 88)
			{
				if (opcQuality != 84 && opcQuality != 88)
				{
					return 4;
				}
			}
			else
			{
				if (opcQuality == 192)
				{
					return 0;
				}
				if (opcQuality != 216)
				{
					return 4;
				}
				return 2;
			}
			return 4;
		}

		// Token: 0x0600003F RID: 63 RVA: 0x00006538 File Offset: 0x00004738
		private bool SetOPCItemsToOffline(EwsValueStateEnum stateEnum)
		{
			InitApp._ILog.Write(ILog.LogLevels.INFO, "SetOPCItemsToOffline...");
			base.DataAdapter.AutoCommit = false;
			foreach (EwsValueItem ewsValueItem in base.DataAdapter.ValueItems)
			{
				ewsValueItem.State = stateEnum;
			}
			base.DataAdapter.CommitChanges();
			base.DataAdapter.AutoCommit = true;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "SetOPCItemsToOffline done");
			return true;
		}

		// Token: 0x06000040 RID: 64 RVA: 0x000065D0 File Offset: 0x000047D0
		public bool SetOPCItemsToOffline(int serverIndex, EwsValueStateEnum stateEnum)
		{
			InitApp._ILog.Write(ILog.LogLevels.INFO, "SetOPCItemsToOffline, server index {0}...", new object[]
			{
				serverIndex
			});
			OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[serverIndex];
			InitApp._ILog.Write(ILog.LogLevels.INFO, "DoUpdatesOPCItemsOffline server '{0}'...", new object[]
			{
				opcClientManager._OPCServerName[opcClientManager._ActiveOPCServerIndex]
			});
			try
			{
				int value = -1;
				string text = string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, "OPC.Status");
				base.DataAdapter.ModifyValueItemValue(text, new int?(value), new EwsValueStateEnum?(0));
				for (int i = 0; i < opcClientManager._lstOPCItems.Count; i++)
				{
					EBOOpcItem eboopcItem = opcClientManager._lstOPCItems[i];
					text = string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, eboopcItem._opcItemDef.ItemID);
					base.DataAdapter.ModifyValueItemValue(text, eboopcItem._opcItemState.DataValue.ToString(), new EwsValueStateEnum?(stateEnum));
				}
				InitApp._ILog.Write(ILog.LogLevels.INFO, "DoUpdatesOPCItemsOffline server '{0}' done", new object[]
				{
					opcClientManager._OPCServerName[opcClientManager._ActiveOPCServerIndex]
				});
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "DoUpdatesOPCItemsOffline server '{0}' error '{1}'", new object[]
				{
					opcClientManager._OPCServerName[opcClientManager._ActiveOPCServerIndex],
					ex.Message
				});
			}
			return true;
		}

		// Token: 0x06000041 RID: 65 RVA: 0x00006740 File Offset: 0x00004940
		private bool DeleteSubscriptionNotifications()
		{
			EwsServerDataAdapter dataAdapter = base.DataAdapter;
			bool result;
			try
			{
				InitApp._ILog.Write(ILog.LogLevels.INFO, "DeleteSubscriptionNotifications...");
				dataAdapter.AutoCommit = false;
				List<EwsSubscription> list = null;
				if (list == null)
				{
					list = dataAdapter.Subscriptions.ToList<EwsSubscription>();
				}
				IList<EwsSubscription> list2 = list;
				IList<EwsSubscription> list3 = list2 ?? list.ToList<EwsSubscription>();
				foreach (EwsSubscription ewsSubscription in list3)
				{
					dataAdapter.DeleteNotifications(ewsSubscription);
				}
				if (list3.Count > 0)
				{
					dataAdapter.DeleteSubscription((from a in list3
					where a.Status != 1
					select a).ToList<EwsSubscription>());
				}
				dataAdapter.CommitChanges();
				dataAdapter.AutoCommit = true;
				InitApp._ILog.Write(ILog.LogLevels.INFO, "DeleteSubscriptionNotifications done");
				result = true;
			}
			catch (SqlException ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "DeleteSubscriptionNotifications, error '{0}'", new object[]
				{
					ex.Message
				});
				result = false;
			}
			return result;
		}

		// Token: 0x06000042 RID: 66 RVA: 0x00006860 File Offset: 0x00004A60
		private bool StopAllOPCServers()
		{
			for (int i = 0; i < ServerHelper._lstOpcClientManager.Count; i++)
			{
				ServerHelper._lstOpcClientManager[i].Stop();
			}
			return true;
		}

		// Token: 0x06000043 RID: 67 RVA: 0x00006894 File Offset: 0x00004A94
		private bool ManageAddItemsOnTheFly()
		{
			bool result;
			try
			{
				FileInfo[] files = new DirectoryInfo(ServerHelper._OPCClientFolder).GetFiles("*.update");
				if (files.Length == 0)
				{
					result = true;
				}
				else
				{
					InitApp._ILog.Write(ILog.LogLevels.INFO, "ManageAddItemsOnTheFly(), # of OPC servers update files: {0}", new object[]
					{
						files.Length
					});
					foreach (FileInfo fileInfo in files)
					{
						int length = fileInfo.Name.Length - ".update".Length;
						length = fileInfo.Name.IndexOf("___");
						string text = fileInfo.Name.Substring(0, length);
						int num = this.FindServerIndexByLogicalName(text);
						if (num < 0)
						{
							InitApp._ILog.Write(ILog.LogLevels.ERROR, "ManageAddItemsOnTheFly(), update file name: '{0}', Logical OPC server name: '{1}'  not found", new object[]
							{
								fileInfo.Name,
								text
							});
							File.Delete(fileInfo.FullName);
							InitApp._ILog.Write(ILog.LogLevels.INFO, "ManageAddItemsOnTheFly(), update file name: '{0}' deleted", new object[]
							{
								fileInfo.Name
							});
						}
						else
						{
							InitApp._ILog.Write(ILog.LogLevels.INFO, "ManageAddItemsOnTheFly(), update file name: '{0}', Logical OPC server name: '{1}', server index: {2}", new object[]
							{
								fileInfo.Name,
								text,
								num
							});
							OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[num];
							if (this.UpdateOPCServerItems(fileInfo.FullName, ref opcClientManager) != 0)
							{
								InitApp._ILog.Write(ILog.LogLevels.ERROR, "ManageAddItemsOnTheFly(), update file name: '{0}', UpdateOPCServerItems fail", new object[]
								{
									fileInfo.Name
								});
								File.Delete(fileInfo.FullName);
								InitApp._ILog.Write(ILog.LogLevels.INFO, "ManageAddItemsOnTheFly(), update file name: '{0}' deleted", new object[]
								{
									fileInfo.Name
								});
							}
							else
							{
								File.Delete(fileInfo.FullName);
								InitApp._ILog.Write(ILog.LogLevels.INFO, "ManageAddItemsOnTheFly(), update file name: '{0}' deleted", new object[]
								{
									fileInfo.Name
								});
							}
						}
					}
					result = true;
				}
			}
			catch (Exception)
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000044 RID: 68 RVA: 0x00006A94 File Offset: 0x00004C94
		private int FindServerIndexByLogicalName(string OPCLogicalName)
		{
			if (string.IsNullOrEmpty(OPCLogicalName))
			{
				return -1;
			}
			for (int i = 0; i < ServerHelper._lstOpcClientManager.Count; i++)
			{
				if (string.Compare(OPCLogicalName, ServerHelper._lstOpcClientManager[i]._OPCLogicalName, true) == 0)
				{
					return i;
				}
			}
			return -1;
		}

		// Token: 0x06000045 RID: 69 RVA: 0x00006ADC File Offset: 0x00004CDC
		private int UpdateOPCServerItems(string UpdateItemsFileName, ref OpcClientManager opcClientManager)
		{
			int result = 0;
			int i = 0;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateOPCServerItems, file: '{0}'", new object[]
			{
				UpdateItemsFileName
			});
			string text = File.ReadAllText(UpdateItemsFileName, Encoding.UTF8);
			if (string.IsNullOrEmpty(text))
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}' ReadAllText() fail", new object[]
				{
					UpdateItemsFileName
				});
				return -1;
			}
			char[] separator = new char[]
			{
				'\n'
			};
			string[] array = text.Split(separator, StringSplitOptions.RemoveEmptyEntries);
			if (array.Length == 0)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}' invalid format!", new object[]
				{
					UpdateItemsFileName
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateOPCServerItems, file: '{0}' # of lines: {1}", new object[]
			{
				UpdateItemsFileName,
				array.Length
			});
			try
			{
				for (i = 0; i < array.Length; i++)
				{
					string text2 = array[i].Trim();
					string[] separator2 = new string[]
					{
						"}}"
					};
					string[] array2 = text2.Split(separator2, StringSplitOptions.RemoveEmptyEntries);
					if (array2.Length != 5)
					{
						InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}' invalid line: '{1}'", new object[]
						{
							UpdateItemsFileName,
							text2
						});
					}
					else
					{
						string text3 = array2[0].Substring(2).Trim();
						string text4 = array2[1].Substring(2).Trim();
						string text5 = array2[2].Substring(2).Trim();
						if (string.IsNullOrEmpty(text5))
						{
							text5 = text4;
						}
						string itemPath = array2[3].Substring(2).Trim();
						string text6 = array2[4].Substring(2).Trim();
						int num = 1;
						char[] separator3 = new char[]
						{
							'['
						};
						string[] array3 = text6.Split(separator3, StringSplitOptions.RemoveEmptyEntries);
						if (array3.Length == 2)
						{
							text6 = array3[0];
							string s = array3[1].Remove(array3[1].Length - 1);
							try
							{
								num = int.Parse(s);
							}
							catch (Exception)
							{
							}
						}
						InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateOPCServerItems, file: '{0}', line: {1}. group: '{2}', itemID: '{3}', EWSItemID: '{4}', type: '{5}', # of items: {6}", new object[]
						{
							UpdateItemsFileName,
							i + 1,
							text3,
							text4,
							text5,
							text6,
							num
						});
						if (opcClientManager._mapItemIDToOpcItem.ContainsKey(text4))
						{
							InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}', error on line: {1}, item '{2}' already exist!", new object[]
							{
								UpdateItemsFileName,
								i + 1,
								text4
							});
						}
						else if (opcClientManager._mapEWSItemIDToOpcItem.ContainsKey(text5))
						{
							InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}', error on line: {1}, item '{2}' already exist!", new object[]
							{
								UpdateItemsFileName,
								i + 1,
								text5
							});
						}
						else
						{
							string text7 = "string";
							if (!InitApp.ConvertOpcTypeToCommonType(text6, out text7))
							{
								InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}', error on line: {1}, item: '{2}', type: '{3}' not define!", new object[]
								{
									UpdateItemsFileName,
									i + 1,
									text5,
									text6
								});
							}
							else
							{
								InitApp._ILog.Write(ILog.LogLevels.DEBUG, "UpdateOPCServerItems, file: '{0}', line: {1}, item: '{2}', type: '{3}' => '{4}'", new object[]
								{
									UpdateItemsFileName,
									i + 1,
									text5,
									text6,
									text7
								});
								int count = opcClientManager._lstOPCItems.Count;
								int address = count;
								EBOOpcItem eboopcItem = new EBOOpcItem(text4, itemPath, true, count, VarEnum.VT_EMPTY, address, text3, text5);
								eboopcItem._DataTypeFromUCME = text7;
								eboopcItem._NumOfItemsInArray = num;
								opcClientManager._lstOPCItems.Add(eboopcItem);
								opcClientManager._lstOPCItemsOnTheFlyUpdate.Add(eboopcItem);
								opcClientManager._mapItemIDToOpcItem.Add(text4, eboopcItem);
								opcClientManager._mapEWSItemIDToOpcItem.Add(text5, eboopcItem);
								this.AddEWSItemsOnTheFly(ref opcClientManager, ref eboopcItem);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "UpdateOPCServerItems, file: '{0}', error on line: {1}, '{2}'", new object[]
				{
					UpdateItemsFileName,
					i + 1,
					ex.Message
				});
				return -1;
			}
			InitApp._ILog.Write(ILog.LogLevels.INFO, "UpdateOPCServerItems, file: '{0}', # of items: {1}", new object[]
			{
				UpdateItemsFileName,
				opcClientManager._lstOPCItems.Count
			});
			return result;
		}

		// Token: 0x06000046 RID: 70 RVA: 0x00006F10 File Offset: 0x00005110
		private bool AddEWSItemsOnTheFly(ref OpcClientManager opcClientManager, ref EBOOpcItem opcItem)
		{
			bool result;
			try
			{
				EwsContainerItem parent = this.EnsureContainerItem(opcClientManager._OPCLogicalName, null, null, 0, null);
				EwsContainerItem parent2 = this.EnsureContainerItem(string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, opcItem._GroupName), null, null, 0, parent);
				EwsValueTypeEnum ewsValueTypeEnum = SetupProcessor.ConvertOPCDateTypeToEWSType(opcItem._DataTypeFromUCME);
				InitApp._ILog.Write(ILog.LogLevels.DEBUG, "add EWS item '{0}', type {1}, # of items {2} of {3}", new object[]
				{
					opcItem._EWSItemID,
					ewsValueTypeEnum,
					1,
					1
				});
				if (opcItem._NumOfItemsInArray <= 1)
				{
					this.EnsureValueItem(string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, opcItem._EWSItemID), opcItem._EWSItemID, opcItem._AccessPath, ewsValueTypeEnum, parent2, null, 1, 1, 1);
				}
				else
				{
					InitApp._ILog.Write(ILog.LogLevels.DEBUG, "add EWS array items: '{0}', type {1}, # of array elements: {2} ", new object[]
					{
						opcItem._EWSItemID,
						ewsValueTypeEnum,
						opcItem._NumOfItemsInArray
					});
					for (int i = 0; i < opcItem._NumOfItemsInArray; i++)
					{
						this.EnsureValueItem(string.Format("[{0}].{1}#[{2}]", opcClientManager._OPCServerIndex, opcItem._EWSItemID, i + 1), string.Format("{0}#[{1}]", opcItem._EWSItemID, i + 1), string.Format("{0}#[{1}]", opcItem._AccessPath, i + 1), ewsValueTypeEnum, parent2, null, 1, 1, 1);
					}
				}
				result = true;
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "AddEWSItemsOnTheFly, EWSItemID: '{0}', type: {1}, fail '{2}'", new object[]
				{
					opcItem._EWSItemID,
					opcItem._DataTypeFromUCME,
					ex.Message
				});
				result = false;
			}
			return result;
		}

		// Token: 0x06000047 RID: 71 RVA: 0x000070F8 File Offset: 0x000052F8
		private EwsContainerItem EnsureContainerItem(string altId, string name = null, string description = null, EwsContainerTypeEnum type = 0, EwsContainerItem parent = null)
		{
			base.CheckCancellationToken();
			EwsContainerItem ewsContainerItem = base.DataAdapter.ContainerItems.FirstOrDefault((EwsContainerItem x) => x.AlternateId == altId);
			if (ewsContainerItem == null)
			{
				return base.DataAdapter.AddContainerItem(altId, name ?? altId, description, type, parent);
			}
			ewsContainerItem = base.DataAdapter.ModifyContainerItemName(ewsContainerItem, altId);
			ewsContainerItem = base.DataAdapter.ModifyContainerItemDescription(ewsContainerItem, description);
			ewsContainerItem = base.DataAdapter.ModifyContainerItemType(ewsContainerItem, type);
			return base.DataAdapter.ModifyContainerItemParent(ewsContainerItem, parent);
		}

		// Token: 0x06000048 RID: 72 RVA: 0x000071F0 File Offset: 0x000053F0
		private EwsValueItem EnsureValueItem(string altId, string name = null, string description = null, EwsValueTypeEnum type = 2, EwsContainerItem parent = null, string unit = null, EwsValueWriteableEnum writeable = 1, EwsValueForceableEnum forceable = 1, EwsValueStateEnum defaultState = 1)
		{
			base.CheckCancellationToken();
			EwsValueItem ewsValueItem = base.DataAdapter.ValueItems.FirstOrDefault((EwsValueItem x) => x.AlternateId == altId);
			if (ewsValueItem == null)
			{
				return base.DataAdapter.AddValueItem(altId, name ?? altId, description, type, writeable, forceable, defaultState, unit, parent, null);
			}
			ewsValueItem = base.DataAdapter.ModifyValueItemName(ewsValueItem, altId);
			ewsValueItem = base.DataAdapter.ModifyValueItemDescription(ewsValueItem, description);
			ewsValueItem = base.DataAdapter.ModifyValueItemType(ewsValueItem, type);
			ewsValueItem = base.DataAdapter.ModifyValueItemWriteable(ewsValueItem, writeable);
			ewsValueItem = base.DataAdapter.ModifyValueItemForceable(ewsValueItem, forceable);
			ewsValueItem = base.DataAdapter.ModifyValueItemUnit(ewsValueItem, unit);
			return base.DataAdapter.ModifyValueItemParent(ewsValueItem, parent);
		}

		// Token: 0x04000059 RID: 89
		private bool hasUpdateEWSItems;

		// Token: 0x0400005A RID: 90
		private DateTime _lastUpdateEWSItems = DateTime.Now;
	}
}
