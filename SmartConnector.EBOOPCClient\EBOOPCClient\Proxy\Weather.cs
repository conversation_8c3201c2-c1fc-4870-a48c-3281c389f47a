﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000016 RID: 22
	public class Weather
	{
		// Token: 0x1700002C RID: 44
		// (get) Token: 0x060000B7 RID: 183 RVA: 0x000082F4 File Offset: 0x000064F4
		// (set) Token: 0x060000B8 RID: 184 RVA: 0x000082FC File Offset: 0x000064FC
		[JsonProperty("id")]
		public long Id { get; set; }

		// Token: 0x1700002D RID: 45
		// (get) Token: 0x060000B9 RID: 185 RVA: 0x00008305 File Offset: 0x00006505
		// (set) Token: 0x060000BA RID: 186 RVA: 0x0000830D File Offset: 0x0000650D
		[JsonProperty("main")]
		public string Main { get; set; }

		// Token: 0x1700002E RID: 46
		// (get) Token: 0x060000BB RID: 187 RVA: 0x00008316 File Offset: 0x00006516
		// (set) Token: 0x060000BC RID: 188 RVA: 0x0000831E File Offset: 0x0000651E
		[JsonProperty("description")]
		public string Description { get; set; }

		// Token: 0x1700002F RID: 47
		// (get) Token: 0x060000BD RID: 189 RVA: 0x00008327 File Offset: 0x00006527
		// (set) Token: 0x060000BE RID: 190 RVA: 0x0000832F File Offset: 0x0000652F
		[JsonProperty("icon")]
		public string Icon { get; set; }
	}
}
