<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ews.Client</name>
    </assembly>
    <members>
        <member name="T:Ews.Client.EwsClient">
            <summary>
            Common client wrapper for communicating over EWS v1.1 or EWS v1.2
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.#ctor(SxL.Common.IEndpoint,System.Nullable{Ews.Common.EwsVersion})">
            <summary>
            Returns a new EwsClient instance.
            </summary>
            <param name="endpoint">Endpoint to connect to</param>
            <param name="compatibilityVersion">Default compatability version to use.  If not supplied maximum server version is used.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.#ctor(Ews.Client.EwsSecurity,System.String,System.Nullable{Ews.Common.EwsVersion})">
            <summary>
            Returns a new EwsClient instance.
            </summary>
            <param name="credentials">Credentials to authenitcate with</param>
            <param name="address">Endpoint address</param>
            <param name="compatibilityVersion">Default compatability version to use.  If not supplied maximum server version is used.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.#ctor(Ews.Client.EwsSecurity,System.String,Ews.Client.EwsBindingConfig,System.Nullable{Ews.Common.EwsVersion})">
            <summary>
            Returns a new EwsClient instance.
            </summary>
            <param name="credentials">Credentials to authenitcate with</param>
            <param name="address">Endpoint address</param>
            <param name="bindingConfig">WCF binding configuration</param>
            <param name="compatibilityVersion">Default compatability version to use.  If not supplied maximum server version is used.</param>
        </member>
        <member name="P:Ews.Client.EwsClient.AuthenticationBehavior">
            <summary>
            Injects authentication information into the request once the server has responded.
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.CreateCustomBinding(System.Boolean)">
            <summary>
            Returns a CustomBinding instance for the protocol requested and the EwsBinding property
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.CompatibilityVersion">
            <summary>
            Allows consumer to manually dictate how requests will be made to the server.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.IncludeMetadata">
            <summary>
            If true, Metadata will be requested on all applicable methods.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.SupportedMethods">
            <summary>
            Returns the list of supported methods reported by the server. 
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.SupportedProfiles">
            <summary>
            Returns the list of supported profiles reported by the server. 
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.ServerVersion">
            <summary>
            Returns the version of the server.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.ServerId">
            <summary>
            Returns the ID of the connected server.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.Namespace">
            <summary>
            Namespace returned by the connected server.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsClient.EwsVersionImplemented">
            <summary>
            Returns the highest EWS version supported by the connected EWS server.
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.GetWebServiceInformation">
            <summary>
            Returns implementation information from the server.  
            The response message will include information about the EWS version, Profiles and methods.  
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.GetEnums(System.String[])">
            <summary>
            Returns Enum from the server when supplied with one or more Enum.Id values.
            </summary>
            <param name="ids">Id of Enum requested.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.ForceValues(Ews.Client.ValueTypeStateless[])">
            <summary>
            Modifies value information for one or more ValueItem in a manner that cannot be overwritten by a call to SetValues.  
            Subsequent calls to ForceValues can be used to change the ValueItem Value again however a SetValues call will fail to modify the value.  
            </summary>
            <param name="items">Force requests.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetContainerItems(System.String)">
            <summary>
            Returns the root node of the server.
            </summary>
            <param name="moreDataRef">Paging parameter.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetContainerItems(System.String[],System.String)">
            <summary>
            Returns ContainerItem when supplied one or more ContainerItem.Id values.  
            </summary>
            <param name="ids">ContainerItem requested.</param>
            <param name="moreDataRef">Paging parameter.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetHierarchicalInformation(System.String)">
            <summary>
            Returns the complete hierarchical path of a ContainerItem, ValueItem, HistoryItem, or AlarmItem back to the root ContainerItem. 
            When the root ContainerItem is requested, the returned hierarchical list is empty because the root ContainerItem does not have a parent.
            </summary>
            <param name="id">Id of a ContainerItem, ValueItem, HistoryItem or AlarmItem.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetItems(System.String[])">
            <summary>
            Returns one or more (ValueItem, HistoryItem, or AlarmItem) from the server in a single call.  
            A ContainerItem cannot be retrieved using this method.  Use GetContainerItems instead.
            </summary>
            <param name="ids">Items requested.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetValues(System.String[])">
            <summary>
            Returns Value and State information the server when supplied with one or more ValueItem.Id values.  
            </summary>
            <param name="ids">Items requested.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.SetValues(Ews.Client.ValueTypeStateless[])">
            <summary>
            Modifies value information for one or more ValueItem in the server.  
            </summary>
            <param name="items">Set requests</param>
        </member>
        <member name="M:Ews.Client.EwsClient.UnforceValues(System.String[])">
            <summary>
            Returns one or more ValueItem to the "Unforced" or normal state after having been "Forced".  
            The value of the ValueItem after being "Unforced" is dependent on the server implementation.
            </summary>
            <param name="ids">Ids of the ValueItem to unforce.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.AcknowledgeAlarmEvents(System.String[])">
            <summary>
            "Acknowledges" an AlarmEvent for one or more AlarmItem as defined in the State Transition Models section of the specification.
            </summary>
            <param name="ids">Ids of the AlarmEvent to acknowledge.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetAlarmEvents">
            <summary>
            Returns an un-filtered set of active AlarmEvent.  An active AlarmEvent is one whose State is either "Active", "Acknowledge", or "Reset" 
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.GetAlarmEvents(System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String[])">
            <summary>
            Returns a filtered set of active AlarmEvent.  An active AlarmEvent is one whose State is either "Active", "Acknowledge", or "Reset".
            </summary>
            <param name="priorityFrom">Lower end of Priority range.  Values from 0-1000.</param>
            <param name="priorityTo">Upper end of Priority range.  Values from 0-1000.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="types">Type returned from GetAlarmEventTyes.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetAlarmEventTypes">
            <summary>
            Returns the list of “types” of AlarmEvent defined or supported by the implementing server.  
            Once obtained, type values can be supplied as input filters to GetAlarmEvents and GetUpdatedAlarmEvents methods
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.GetAlarmHistory(System.String)">
            <summary>
            Returns unfiltered AlarmEvent data for a single AlarmItem over a specified time range regardless State.
            </summary>
            <param name="alarmItemId">Id of the AlarmItem historical AlarmEvent is being requested for.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetAlarmHistory(System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String[],System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Returns filtered AlarmEvent data for a single AlarmItem over a specified time range regardless State.
            </summary>
            <param name="alarmItemId">Id of the AlarmItem historical AlarmEvent is being requested for.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="priorityFrom">Lower end of Priority range.  Values from 0-1000.</param>
            <param name="priorityTo">Upper end of Priority range.  Values from 0-1000.</param>
            <param name="types">Type returned from GetAlarmEventTyes.</param>
            <param name="timeFrom">Only AlarmEvent with TimeStampTransition value on or after this value will be returned.</param>
            <param name="timeTo">Only AlarmEvent with TimeStampTransition value on or before this value will be returned.  </param>
            <returns></returns>
        </member>
        <member name="M:Ews.Client.EwsClient.GetUpdatedAlarmEvents(System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String[],System.String)">
            <summary>
            Returns a filtered set of AlarmEvent that have changed State since the last request.
            </summary>
            <param name="priorityFrom">Lower end of Priority range.  Values from 0-1000.</param>
            <param name="priorityTo">Upper end of Priority range.  Values from 0-1000.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="types">Type returned from GetAlarmEventTyes.</param>
            <param name="lastUpdate">Server defined value indicating a point in time from which AlarmEvent should be returned.</param>
            <returns></returns>
        </member>
        <member name="M:Ews.Client.EwsClient.GetUpdatedAlarmEvents(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String[])">
            <summary>
            Returns a filtered set of AlarmEvent that have changed State since the last request.
            </summary>
            <param name="lastUpdate">Server defined value indicating a point in time from which AlarmEvent should be returned.</param>
            <param name="priorityFrom">Lower end of Priority range.  Values from 0-1000.</param>
            <param name="priorityTo">Upper end of Priority range.  Values from 0-1000.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="types">Type returned from GetAlarmEventTyes.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetHistory(System.String)">
            <summary>
            Returns un-filtered historical data stored for a given HistoryItem
            </summary>
            <param name="id">Id of the HistoryItem for which history is requested.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetHistory(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Returns filtered historical data stored for a given HistoryItem
            </summary>
            <param name="id">Id of the HistoryItem for which history is requested.</param>
            <param name="moreDataRef">Paging parameter</param>
            <param name="timeFrom">Only history which was logged on or after this value will be returned.  </param>
            <param name="timeTo">Only history which was logged on or before this value will be returned.  </param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetHistoricalDataAggregation(System.String,System.String,Ews.Common.EwsAggregationGroupEnum,Ews.Common.EwsAggregationTypeEnum,System.Int32[],System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Returns calculated aggregate values for a single HistoryItem over a specific date range.  
            </summary>
            <param name="id">Id of the HistoryItem for which aggregated history is requested.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="group">How history will be grouped.</param>
            <param name="type">The type of aggregation to be performed.</param>
            <param name="periods">Specific periods in the group.</param>
            <param name="timeFrom">Only history which was logged on or after this value will be returned.</param>
            <param name="timeTo">Only history which was logged on or before this value will be returned.  </param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetSystemEvents(System.String[],System.String)">
            <summary>
            Returns an un-filtered set of SystemEvents
            </summary>
            <param name="ids"></param>
            <param name="moreDataRef"></param>
        </member>
        <member name="M:Ews.Client.EwsClient.GetSystemEvents(System.String[],System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String[])">
            <summary>
            Returns a filtered set of SystemEvents.
            </summary>
            <param name="ids">SystemEvents with this Id to return.</param>
            <param name="moreDataRef">Paging parameter.</param>
            <param name="priorityFrom">Lower end of Priority range.  Values from 0-1000.</param>
            <param name="priorityTo">Upper end of Priority range.  Values from 0-1000.</param>
            <param name="timeFrom">Only SystemEvents which occurred on or after this value will be returned. </param>
            <param name="timeTo">Only SystemEvents which occurred on or before this value will be returned.</param>
            <param name="types">SystemEvents Types to return.</param>
            <returns></returns>
        </member>
        <member name="M:Ews.Client.EwsClient.GetSystemEventTypes">
            <summary>
            Returns the list of "types" of SystemEvents defined or supported by the implementing server.  Once obtained, type values can be supplied as input filters to GetSystemEvents.  
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.GetNotification(System.String,System.String,System.String)">
            <summary>
            "Pulls" subscribed events from the server.  
            </summary>
            <param name="subscriptionId">Id of the Subscription for which notification was requested.</param>
            <param name="notificationId">Identifier for the response session.</param>
            <param name="moreDataRef">Paging parameter.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.Renew(System.String,System.Nullable{System.Int32})">
            <summary>
            Extends an existing valid Subscription without need to establish a new subscription session.
            </summary>
            <param name="subscriptionId">Id of the subscription to renew.</param>
            <param name="expirationInMinutes">Minutes to extend the subscription.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.Subscribe(Ews.Common.EwsSubscriptionEventTypeEnum,System.String[],System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String[],Ews.Common.EwsEventIdTypeModeEnum)">
            <summary>
            Registers event notification with the implementing server.
            </summary>
            <param name="type">Type of event being registered.</param>
            <param name="ids">Ids in the filter.</param>
            <param name="expirationInMinutes">Minutes to extend the subscription.</param>
            <param name="priorityFrom">Alarm prioritization filter value.</param>
            <param name="priorityTo">Alarm prioritization filter value.</param>
            <param name="types">Alarm filter value.</param>
            <param name="idTypeMode">How the ids will be interpretted by the server.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.Unsubscribe(System.String)">
            <summary>
            Sancels an active subscription.
            </summary>
            <param name="subscriptionId">Id of the subscription to unsubscribe from.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.IsMethodSupported(Ews.Common.EwsMethodCall)">
            <summary>
            Returns true if the method supplied is supported by the server.
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.IsMethodSupported(System.String)">
            <summary>
            Returns true if the method supplied is supported by the server.
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.ExecuteAndLogCall``2(System.String,``0,System.Func{``0,``1})">
            <summary>
            Helper method to log the EWS consume call occured, it's request object and response object and any error that may have occured.
            </summary>
        </member>
        <member name="M:Ews.Client.EwsClient.ExtractHistoryItemIds(System.Boolean,System.Collections.Generic.List{System.String},System.Int32)">
            <summary>
            Navigates the EWS connection and returns a list of history item IDs.
            </summary>
            <param name="containerItemIds">list of container item IDs in which to search</param>
            <param name="maxDepth">how deep in the tree this method will search</param>
            <param name="startSearchAtRootContainerItem">Set to true if the search should start at the root of the EWS server. In this case, containerItemIds will be ignored.</param>
        </member>
        <member name="M:Ews.Client.EwsClient.ExtractHistoryItems(System.Boolean,System.Collections.Generic.List{System.String},System.Int32,System.Int32,System.Collections.Generic.List{Ews.Client.HistoryItemType})">
            <summary>
            Navigates the EWS connection and returns a list of history item IDs.
            </summary>
            <param name="containerItemIds">list of container item IDs in which to search</param>
            <param name="maxDepth">how deep in the tree this method will search</param>
            <param name="startSearchAtRootContainerItem">Set to true if the search should start at the root of the EWS server. In this case, containerItemIds will be ignored.</param>
            <param name="currentDepth"></param>
            <param name="currentList"></param>
        </member>
        <member name="P:Ews.Client.EwsClient.DefaultBindingConfig">
            <summary>
            Returns the default EwsBindingConfig used by the client.
            </summary>
        </member>
        <member name="T:Ews.Client.EwsBindingConfig">
            <summary>
            Contains the configuration parameter of the client instance to the communication layer.
            </summary>
        </member>
        <member name="T:Ews.Client.IManagedEwsClient">
            <summary>
            Interface for a managed EwsClient instance
            </summary>
        </member>
        <member name="T:Ews.Client.EwsSecurity">
            <summary>
            Contains the security data which are used by the client instance to communicate with the server
            </summary>
        </member>
        <member name="P:Ews.Client.EwsSecurity.UserName">
            <summary>
            User name for authentication.
            </summary>
        </member>
        <member name="P:Ews.Client.EwsSecurity.Password">
            <summary>
            Password for authentication.
            </summary>
        </member>
        <member name="T:Ews.Client.ExtensionMethods">
            <summary>
            General repository for extension methods
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetContainerTypeEnum(Ews.Client.ContainerItemType)">
            <summary>
            Coerces the Type parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetAlarmStateEnum(Ews.Common.IEwsHasAlarmStateAttribute)">
            <summary>
            Coerces the State parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetAlarmAcknowledgeableEnum(Ews.Client.AlarmEventsType)">
            <summary>
            Coerces the Acknowledgeable parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetValueTypeEnum(Ews.Common.IEwsHasValueTypeAttribute)">
            <summary>
            Coerces the Type parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetValueStateEnum(Ews.Common.IEwsHasValueStateAttribute)">
            <summary>
            Coerces the State parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetValueForceableEnum(Ews.Client.ValueItemType)">
            <summary>
            Coerces the Forceable parameter into a definitive enumeration value.
            </summary>
        </member>
        <member name="M:Ews.Client.ExtensionMethods.GetValueWriteableEnum(Ews.Client.ValueItemType)">
            <summary>
            Coerces the Writeable parameter into a definitive enumeration value.
            </summary>
        </member>
    </members>
</doc>
