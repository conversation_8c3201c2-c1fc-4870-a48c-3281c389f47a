<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Mongoose.Ews.Server</name>
    </assembly>
    <members>
        <member name="T:Mongoose.Ews.Server.MongooseEwsServiceHost">
            <summary>
            Base implementation for an EwsServiceHost for Mongoose's default EWS serve implementation.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.MongooseEwsServiceHost.IsLicensed">
            <summary>
            By default, SmartConnector EWS Servers are NOT licensed.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.MongooseEwsServiceHost.ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense)">
            <summary>
            Custom feature license enforcement is up to the implementing extension.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.Extensions.AsEwsValue(System.String,Ews.Common.EwsValueTypeEnum)">
            <summary>
            EWS mandates values in a certain format.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.IMongooseValueChangeProcessor.ValueItem">
            <summary>
            ValueItem the processor is acting on
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.IMongooseValueChangeProcessor.ValueValidator">
            <summary>
            Validator for an ValueItem value.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetEnumsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetEnumsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseForceValuesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseForceValuesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseForceValuesProcessor.ValueValidator">
            <summary>
            Value validator for any EwsValueItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetAlarmHistoryProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetAlarmHistoryProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetAlarmHistoryProcessor.ExtractPagingData(System.String)">
            <summary>
            Extract cached paging data and re-hydrate all cached filter parameters
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetHierarchicalInformationProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetHierarchicalInformationProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetHistoricalDataAggregationProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetHistoricalDataAggregationProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetNotificationProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetNotificationProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetSystemEventsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetSystemEventsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetSystemEventTypesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetSystemEventTypesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseAcknowledgeAlarmEventsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseAcknowledgeAlarmEventsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseAcknowledgeAlarmEventsProcessor.CurrentAlarmEvent">
            <summary>
            The EwsAlarmEvent currently being processed.  Set before AcknowledgeAlarm is called. 
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetAlarmEventsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetAlarmEventsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetAlarmEventTypesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetAlarmEventTypesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetContainerItemsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetContainerItemsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetHistoryProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetHistoryProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="F:Mongoose.Ews.Server.Processor.MongooseGetHistoryProcessor.HistoryItem">
            <summary>
            HistoryItem history is being requested for.  Retrieved during ID validation.
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetItemsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetItemsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetUpdatedAlarmEventsProcessor.LastUpdate">
            <summary>
            Set during ExtractPagingData.  DateTime of the last results determined.  
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetUpdatedAlarmEventsProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetUpdatedAlarmEventsProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetValuesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetValuesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseGetWebServiceInformationProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseGetWebServiceInformationProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseSetValuesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseSetValuesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseSetValuesProcessor.ValueValidator">
            <summary>
            Value validator for any EwsValueItem
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseRenewProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseRenewProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseSubscribeProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseSubscribeProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseUnforceValuesProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseUnforceValuesProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
        <member name="P:Mongoose.Ews.Server.Processor.MongooseUnsubscribeProcessor.ProcessorValueSource">
            <summary>
            Lazily instantiated data source for accessing ProcessorValue data.  It is important to consume this in a using block to ensure that the connection remains current with the state of the database.  
            Otherwise, the connection will be automatically disposed of when the Processor is disposed of.
            </summary>
        </member>
        <member name="M:Mongoose.Ews.Server.Processor.MongooseUnsubscribeProcessor.DisposeOfProcessorValueDataSource">
            <summary>
            Safely cleans up our reference to IProcessorValueDataSource
            </summary>
        </member>
    </members>
</doc>
