﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Mongoose.Process;
using SxL.Common;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x0200000C RID: 12
	public abstract class EBOOpcClientProcessorBase : Processor
	{
		// Token: 0x17000004 RID: 4
		// (get) Token: 0x0600004A RID: 74 RVA: 0x0000732F File Offset: 0x0000552F
		// (set) Token: 0x0600004B RID: 75 RVA: 0x00007337 File Offset: 0x00005537
		[Required]
		[DefaultValue("SmartConnector OPC Client Service")]
		[Tooltip("Name of the EWS Server to connect to or bootstrap")]
		public string ServerName { get; set; }

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x0600004C RID: 76 RVA: 0x00007340 File Offset: 0x00005540
		// (set) Token: 0x0600004D RID: 77 RVA: 0x00007348 File Offset: 0x00005548
		[Required]
		[EncryptedString]
		[DefaultValue("admin")]
		public string UserName { get; set; }

		// Token: 0x17000006 RID: 6
		// (get) Token: 0x0600004E RID: 78 RVA: 0x00007351 File Offset: 0x00005551
		// (set) Token: 0x0600004F RID: 79 RVA: 0x00007359 File Offset: 0x00005559
		[Required]
		[EncryptedString]
		[DefaultValue("Admin!23")]
		public string Password { get; set; }

		// Token: 0x17000007 RID: 7
		// (get: 0x06000050 RID: 80 RVA: 0x00007362 File Offset: 0x00005562
		// (set): 0x06000051 RID: 81 RVA: 0x00007369 File Offset: 0x00005569
		[Required]
		[DefaultValue("C:\\Program Files (x86)\\Control-See\\EBO-OPC client")]
		public string OPCClientFolder
		{
			get
			{
				return ServerHelper._OPCClientFolder;
			}
			set
			{
				ServerHelper._OPCClientFolder = value;
			}
		}

		// Token: 0x17000008 RID: 8
		// (get): 0x06000052 RID: 82 RVA: 0x00007371 File Offset: 0x00005571
		// (set): 0x06000053 RID: 83 RVA: 0x00007378 File Offset: 0x00005578
		[Required]
		[DefaultValue("C:\\Program Files (x86)\\Control-See\\EBO-OPC client\\Logs")]
		public string OPCClientLogFolder
		{
			get
			{
				return ServerHelper._OPCClientLogFolder;
			}
			set
			{
				ServerHelper._OPCClientLogFolder = value;
			}
		}

		// Token: 0x06000054 RID: 84 RVA: 0x00007380 File Offset: 0x00005580
		public override IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
		{
			if (!string.IsNullOrEmpty(this.UserName) && !string.IsNullOrEmpty(this.Password) && this.UserName == this.Password)
			{
				yield return new ValidationResult("UserName and Password cannot be the same", new string[]
				{
					"UserName",
					"Password"
				});
			}
			foreach (ValidationResult validationResult in base.Validate(validationContext))
			{
				yield return validationResult;
			}
			IEnumerator<ValidationResult> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x17000009 RID: 9
		// (get): 0x06000055 RID: 85 RVA: 0x00007398 File Offset: 0x00005598
		// (set): 0x06000056 RID: 86 RVA: 0x00007414 File Offset: 0x00005614
		protected EwsServerDataAdapter DataAdapter
		{
			get
			{
				if (this._dataAdapter != null)
				{
					return this._dataAdapter;
				}
				try
				{
					this._dataAdapter = EwsServerDataAdapter.ConnectExisting(this.ServerName, this.UserName, this.Password);
				}
				catch (ApplicationException ex)
				{
					Logger.LogError(LogCategory.Processor, ex, Array.Empty<object>());
				}
				EwsServerDataAdapter result;
				if ((result = this._dataAdapter) == null)
				{
					result = (this._dataAdapter = this.CreateEwsServer());
				}
				return result;
			}
			set
			{
				this._dataAdapter = value;
			}
		}

		// Token: 0x1700000A RID: 10
		// (get): 0x06000057 RID: 87 RVA: 0x0000741D File Offset: 0x0000561D
		protected bool IsConnected
		{
			get
			{
				return this.DataAdapter != null;
			}
		}

		// Token: 0x06000058 RID: 88 RVA: 0x00007428 File Offset: 0x00005628
		protected virtual EwsServerDataAdapter CreateEwsServer()
		{
			return null;
		}

		// Token: 0x06000059 RID: 89 RVA: 0x0000742B File Offset: 0x0000562B
		protected Prompt CreateCannotConnectPrompt()
		{
			return new Prompt
			{
				Message = "Failed to connect to a valid EwsServerDataAdapter.",
				Severity = (PromptSeverity)1
			};
		}

		// Token: 0x0600005D RID: 93 RVA: 0x0000748F File Offset: 0x0000568F
		protected Prompt InitFailPrompt()
		{
			return new Prompt
			{
				Message = "Failed to initialize.",
				Severity = (PromptSeverity)1
			};
		}

		// Token: 0x0600005E RID: 94 RVA: 0x000074A8 File Offset: 0x000056A8
		protected override void Dispose(bool disposing)
		{
			base.Dispose(disposing);
			if (this._disposed)
			{
				return;
			}
			EwsServerDataAdapter dataAdapter = this._dataAdapter;
			if (dataAdapter != null)
			{
				dataAdapter.Dispose();
			}
			this._disposed = true;
		}

		// Token: 0x0400005E RID: 94
		private EwsServerDataAdapter _dataAdapter;

		// Token: 0x0400005F RID: 95
		protected static bool _IsInit = false;

		// Token: 0x04000060 RID: 96
		protected static int _InitStatus = -1;

		// Token: 0x04000061 RID: 97
		protected static bool _IsStart = false;

		// Token: 0x04000062 RID: 98
		protected static int _StartStatus = -1;

		// Token: 0x04000063 RID: 99
		private bool _disposed;

		// Override license-related methods to enable licensing
		public override bool IsLicensed
		{
			get { return true; } // Enable licensing requirement
		}

		public override IEnumerable<Prompt> ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense license)
		{
			var prompts = new List<Prompt>();

			try
			{
				// Check UCME license
				UCMECheckLicense.UCMELicInfo licInfo;
				int result = UCMECheckLicense.CheckLicense(out licInfo);

				if (result != 0)
				{
					prompts.Add(new Prompt
					{
						Message = "License validation failed. Please ensure you have a valid license.",
						PromptType = PromptTypes.Error
					});
				}
				else if (licInfo.m_bDemo)
				{
					if (!licInfo.m_ExpertionUnlimited && DateTime.Now >= licInfo.m_dtExpierd)
					{
						prompts.Add(new Prompt
						{
							Message = $"License has expired on {licInfo.m_dtExpierd.ToShortDateString()}. Please renew your license.",
							PromptType = PromptTypes.Error
						});
					}
					else if (!licInfo.m_ExpertionUnlimited)
					{
						prompts.Add(new Prompt
						{
							Message = $"License will expire on {licInfo.m_dtExpierd.ToShortDateString()}.",
							PromptType = PromptTypes.Warning
						});
					}
				}

				// Store license info globally for other components to use
				Global._LicInfo = licInfo;
			}
			catch (Exception ex)
			{
				prompts.Add(new Prompt
				{
					Message = $"License validation error: {ex.Message}",
					PromptType = PromptTypes.Error
				});
			}

			return prompts;
		}
	}
}
