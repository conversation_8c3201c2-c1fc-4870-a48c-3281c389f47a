﻿using System;
using System.Collections;
using Ews.Server.Contract;
using Mongoose.Ews.Server.Processor;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient.EwsServer
{
	// Token: 0x0200001B RID: 27
	public class CustomSetValuesProcessor : MongooseSetValuesProcessor
	{
		// Token: 0x060000E4 RID: 228 RVA: 0x0000847C File Offset: 0x0000667C
		protected override ResultType SetValue(ValueTypeStateless item)
		{
			ResultType resultType = new ResultType();
			resultType.Id = item.Id;
			resultType.Success = true;
			InitApp._ILog.Write(ILog.LogLevels.INFO, "SetValue item id: '{0}', value: '{1}'", new object[]
			{
				item.Id,
				item.Value
			});
			int serverPrefixIndex = this.GetServerPrefixIndex(item.Id);
			InitApp._ILog.Write(ILog.LogLevels.DEBUG, "SetValue item id: '{0}', value: '{1}', server index: {2}", new object[]
			{
				item.Id,
				item.Value,
				serverPrefixIndex
			});
			if (serverPrefixIndex >= 0)
			{
				OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[serverPrefixIndex];
				string text = item.Id.Substring(4);
				int num = -1;
				int num2 = text.IndexOf("#[");
				if (num2 > 0)
				{
					string text2 = text.Substring(num2 + 2);
					text2 = text2.Remove(text2.Length - 1);
					try
					{
						num = int.Parse(text2);
					}
					catch (Exception)
					{
					}
					text = text.Substring(0, num2);
				}
				EBOOpcItem eboopcItem;
				if (opcClientManager._mapEWSItemIDToOpcItem.TryGetValue(text, out eboopcItem))
				{
					if (num == -1)
					{
						InitApp._ILog.Write(ILog.LogLevels.DEBUG, "SetValue item, id: '{0}', value: '{1}', server index: {2} (before write)", new object[]
						{
							item.Id,
							item.Value,
							serverPrefixIndex
						});
						int num3 = opcClientManager.WriteItem(ref eboopcItem, item.Value, true);
						InitApp._ILog.Write(ILog.LogLevels.INFO, "SetValue item, id: '{0}', value: '{1}', server index: {2}, rc = {3}", new object[]
						{
							item.Id,
							item.Value,
							serverPrefixIndex,
							num3
						});
						return resultType;
					}
					if (eboopcItem._opcItemState.DataValue == null)
					{
						return resultType;
					}
					object dataValue = eboopcItem._opcItemState.DataValue;
					Type elementType = dataValue.GetType().GetElementType();
					InitApp._ILog.Write(ILog.LogLevels.DEBUG, "SetValue array item, id: '{0}', value: '{1}', server index: {2}, array index {3}, item type {4} (before write)", new object[]
					{
						item.Id,
						item.Value,
						serverPrefixIndex,
						num,
						dataValue.GetType().GetElementType()
					});
					try
					{
						Array array = dataValue as Array;
						object value = array.GetValue(num - 1);
						value = Convert.ChangeType(item.Value, elementType);
						array.SetValue(value, num - 1);
						IEnumerator enumerator = array.GetEnumerator();
						int num4 = 0;
						while (enumerator.MoveNext() && enumerator.Current != null)
						{
							InitApp._ILog.Write(ILog.LogLevels.DEBUG, "[{0}] = {1}", new object[]
							{
								num4,
								enumerator.Current
							});
							num4++;
						}
						int num5 = opcClientManager.WriteItem(ref eboopcItem, dataValue, true);
						InitApp._ILog.Write(ILog.LogLevels.INFO, "SetValue array item, id: '{0}', value: '{1}', server index: {2}, array index {3}, rc = {4}", new object[]
						{
							item.Id,
							item.Value,
							serverPrefixIndex,
							num,
							num5
						});
						return resultType;
					}
					catch (Exception ex)
					{
						InitApp._ILog.Write(ILog.LogLevels.ERROR, "SetValue array item, id: '{0}', value: '{1}', server index: {2}, array index {3}, rc = {4}", new object[]
						{
							item.Id,
							item.Value,
							serverPrefixIndex,
							num,
							ex.Message
						});
						return resultType;
					}
				}
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "SetValue item id: '{0}', value: '{1}', server index: {2} item not found", new object[]
				{
					item.Id,
					item.Value,
					serverPrefixIndex
				});
				return resultType;
			}
			return base.SetValue(item);
		}

		// Token: 0x060000E5 RID: 229 RVA: 0x00008800 File Offset: 0x00006A00
		private int GetServerPrefixIndex(string ewsItemID)
		{
			if (ewsItemID.Substring(0, 1) == "[" && ewsItemID.Substring(2, 1) == "]")
			{
				int num = int.Parse(ewsItemID.Substring(1, 1));
				if (num < ServerHelper._lstOpcClientManager.Count)
				{
					return num;
				}
			}
			return -1;
		}
	}
}
