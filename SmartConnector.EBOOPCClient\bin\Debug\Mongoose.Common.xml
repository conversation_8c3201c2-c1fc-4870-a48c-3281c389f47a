<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Mongoose.Common</name>
    </assembly>
    <members>
        <member name="T:Mongoose.Common.Api.BaseApiController">
            <summary>
            Base class for any of our API controllers.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.BaseApiController.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.BaseApiController.ControllerName">
            <inheritdoc />
            <summary>
            Inferred from the class name.  Override as needed.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.BaseApiController.LoggingCategory">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.BaseApiController.DeferDisposal(System.IDisposable)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.BaseApiController.UnescapeDataString(System.String)">
            <summary>
            Variation of Uri.UnescapeDataString which handles a null value better.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.ThrowException(Mongoose.Common.Api.IMongooseApiController,System.Net.Http.HttpRequestMessage,System.Exception)">
            <summary>
            Called from the "log" extension methods to provide a way to throw validation information to consumers.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.AddCountHeaderToResponse(Mongoose.Common.Api.IMongooseApiController,System.Net.Http.HttpResponseMessage,System.Int64)">
            <summary>
            Adds the X-Total-Count header to the supplied response message.
            </summary>
            <param name="controller"></param>
            <param name="response">Respnse message.</param>
            <param name="count">Count value to add.</param>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.AddLinkHeadersToResponse(Mongoose.Common.Api.IMongooseApiController,System.Net.Http.HttpResponseMessage,System.Int32,System.Int32,System.Int32)">
            <summary>
            Calculates and adds a Link header to the supplied response message.
            </summary>
            <param name="controller"></param>
            <param name="response"></param>
            <param name="totalCount"></param>
            <param name="take"></param>
            <param name="skip"></param>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.AddLinkHeadersToResponse(Mongoose.Common.Api.IMongooseApiController,System.Net.Http.HttpResponseMessage,System.Collections.Generic.List{Mongoose.Common.Api.LinkHeader})">
            <summary>
            Adds a Link header to the supplied response message.
            </summary>
            <param name="controller"></param>
            <param name="response">Response message.</param>
            <param name="linkHeaders">LinkHeaders which will be added.</param>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.ExecuteRequestAndHandleErrors(Mongoose.Common.Api.IMongooseApiController,System.Action)">
            <summary>
            Helper to handle errors which may occrur in a uniform manner throught out all API endpoints.  Use from any HTTP endpoint method handler.  
            </summary>
            <remarks>
            It is assumed that the methodToCall handles all required logging via the ExecuteAndLogCall extension method.
            </remarks>
            <param name="controller">Controler making the call</param>
            <param name="methodToCall">Action to call.  It is assumed that this method is using the lower ExecuteAndLogCall extension.</param>
        </member>
        <member name="M:Mongoose.Common.Api.ApiExtensionMethods.ExecuteRequestAndHandleErrors``1(Mongoose.Common.Api.IMongooseApiController,System.Func{``0})">
            <summary>
            Helper to handle errors which may occrur in a uniform manner throught out all API endpoints.  Use from any HTTP endpoint method handler.  
            </summary>
            <remarks>
            It is assumed that the methodToCall handles all required logging via the ExecuteAndLogCall extension method.
            </remarks>
            <param name="controller">Controler making the call</param>
            <param name="methodToCall">Action to call.  It is assumed that this method is using the lower ExecuteAndLogCall extension.</param>
        </member>
        <member name="T:Mongoose.Common.Api.CoreOAuthProviderBase">
            <summary>
            Base class for any REST OAuth authentication provider.  
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreOAuthProviderBase.#ctor(System.String)">
            <summary>
            Creates an instance of this class with default behavior
            </summary>
            <param name="publicClientId">Value for PublicClientId property.</param>
        </member>
        <member name="M:Mongoose.Common.Api.CoreOAuthProviderBase.TokenEndpoint(Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.CoreOAuthProviderBase.ValidateClientRedirectUri(Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.CoreOAuthProviderBase.ValidateClientAuthentication(Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext)">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Api.IProviderWithCache">
            <summary>
            Interface for a RestProvider which leverages the InMemoryCache
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.IProviderWithCache.CacheTenantId">
            <summary>
            InMemoryCache TenantID.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.IProviderWithCache.InMemoryCache">
            <summary>
            Dependency injected access to the InMemoryCache
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.CoreUserManager`2">
            <summary>
            Core UserManager base class for REST Extension development and Portal authentication.
            </summary>
            <typeparam name="TUser">Type of the user</typeparam>
            <typeparam name="TUserId">Type of the Id of the user</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.CoreUserManager`2.#ctor(Microsoft.AspNet.Identity.IUserStore{`0,`1})">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.CoreUserManager`2.CacheTenantId">
            <summary>
            InMemoryCache TenantID.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreUserManager`2.GenerateClaimsForUser(`0)">
            <summary>
            Returns a list of claims for the supplied user which the implementing manager requires.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreUserManager`2.GenerateUserIdentityAsync(`0,System.String)">
            <summary>
            Creates a new ClaimsIdentity complete with all claims
            </summary>
            <param name="user">Authenticated user</param>
            <param name="authenticationType">Type of authentication for the claims identity</param>
        </member>
        <member name="T:Mongoose.Common.Api.RestExtensionControllerBase`1">
            <summary>
            Common base class for all any controller used by a RestExtension.  
            </summary>
            <typeparam name="TDataProvider">Type of the Provider used by the REST stack.  This can be an interface or similar and not the actual type activated by EnsureProvider.</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.RestExtensionControllerBase`1.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.RestExtensionControllerBase`1.LoggingCategory">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestExtensionControllerBase`1.EnsureProvider">
            <summary>
            Bootstraps Provider from the CurrentPrincipal.  If a Provider cannot be created, will throw an InvalidRequest exception.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestExtensionControllerBase`1.Provider">
            <summary>
            Returns the EwsServerRestProvider inferred from the current context.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestExtensionControllerBase`1.ExecuteCallWithLoggingAndReturnMessage``2(System.String,``0,System.Func{``0,``1},System.Func{``1,System.Net.Http.HttpResponseMessage})">
            <summary>
            Helper method to log any ApiController call which returns an HttpResponseMessage. 
            </summary>
            <typeparam name="TRequest">Request type.</typeparam>
            <typeparam name="TResponse">Embedded response type.</typeparam>
            <param name="methodName">Name of the controller method being called.</param>
            <param name="request">Request object</param>
            <param name="methodToLog">Inner method to log</param>
            <param name="createResponse">Delegate which will return an HttpResponseMessage from the logged method result.</param>
        </member>
        <member name="M:Mongoose.Common.Api.RestExtensionControllerBase`1.ExecuteCallWithLoggingAndReturnMessage``1(System.String,``0,System.Action{``0},System.Func{System.Net.Http.HttpResponseMessage})">
            <summary>
            Helper method to log any ApiController call which returns an HttpResponseMessage. 
            </summary>
            <typeparam name="TRequest">Request type.</typeparam>
            <param name="methodName">Name of the controller method being called.</param>
            <param name="request">Request object</param>
            <param name="methodToLog">Inner method to log</param>
            <param name="createResponse">Delegate which will return an HttpResponseMessage from the logged method result.</param>
        </member>
        <member name="T:Mongoose.Common.Api.RestProviderSimpleBase">
            <summary>
            Common, non-generic, base class for any REST provider.  Providers supply the structure which will be used at runtime to standup and execute an OWin self-hosted REST endpoint.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.Name">
            <summary>
            The name of the Configuration that instantiated this provider.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.ConfigurationId">
            <summary>
            The Id of the RestConfiguration which hydrated this provider.  Available to sub-classes as a convenience.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.Scheme">
            <summary>
            The scheme used to standup the REST endpoint (e.g. HTTP or HTTPS)
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.Host">
            <summary>
            THe host name for the REST endpoint.  (e.g. localhost, MyMachineName, or 127.0.0.1)
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.Port">
            <summary>
            The port for the REST endpoint.  
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.BaseRoute">
            <summary>
            Optional "base" route for the REST endpoint.  Endpoints will be provisioned as follows: Scheme://Host:Port/BaseRoute
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.Endpoint">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestProviderSimpleBase.GetHttpConfiguration">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderSimpleBase.IsLicensed">
            <summary>
            Indicates to the runtime framework whether the author has requested Extension License enforcement.
            </summary>
            <remarks>
            If you wish to exclude licensing support for this Provider in your assembly, override and return false.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.Api.RestProviderSimpleBase.ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestProviderSimpleBase.OnValidateInstanceCompleted(System.Collections.Generic.List{Mongoose.Common.Prompt})">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestProviderSimpleBase.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestProviderSimpleBase.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Api.RestSignInManagerBase`2">
            <summary>
            SignInManager base class for REST extension development.
            </summary>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.RestSignInManagerBase`2.#ctor(Microsoft.AspNet.Identity.UserManager{`0,`1},Microsoft.Owin.Security.IAuthenticationManager)">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Api.RestUserManagerBase`8">
            <summary>
            UserManager base class for REST extension development.
            </summary>
            <remarks>
            If data is required in order to retrieve users etc, then that data should be handled here as properties and an overloaded contstructor given to supply those properties from 
            </remarks>
            <typeparam name="TDataProvider">RestProviderBase subclass type.</typeparam>
            <typeparam name="THttpConfiguration">RestHttpConfigurationBase subclass type.</typeparam>
            <typeparam name="TUserStore">RestUserStoreBase subclass type.</typeparam>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
            <typeparam name="TSignInManager">RestSignInManagerBase subclass type.</typeparam>
            <typeparam name="TUserManager">The type name of the class which is subclassing this class.</typeparam>
            <typeparam name="TOAuthProvider">RestOAuthProviderBase subclass type.</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.RestUserManagerBase`8.#ctor(Microsoft.AspNet.Identity.IUserStore{`3,`4})">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserManagerBase`8.VerifyPasswordAsync(Microsoft.AspNet.Identity.IUserPasswordStore{`3,`4},`3,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserManagerBase`8.VerifyPassword_Subclass(`3,System.String)">
            <summary>
            If we let the base to this (in VerifyPasswordAsync) it will call the EwsUserStore and want hashing.  We don't need to do that.
            As a result, we override and seal an override which calls into this method to implement verification.
            </summary>
            <param name="user">User to validate</param>
            <param name="password">Password supplied</param>
            <returns></returns>
        </member>
        <member name="M:Mongoose.Common.Api.RestUserManagerBase`8.GenerateClaimsForUser(`3)">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Api.RestUserStoreBase`2">
            <summary>
            UserStore base class for REST extension development.
            </summary>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.GetAccessFailedCountAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.GetLockoutEnabledAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.GetLockoutEndDateAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.IncrementAccessFailedCountAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.ResetAccessFailedCountAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.SetLockoutEnabledAsync(`0,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.SetLockoutEndDateAsync(`0,System.DateTimeOffset)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.FindByIdAsync(`1)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.FindByNameAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.CreateAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.UpdateAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.DeleteAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.SetPasswordHashAsync(`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.GetPasswordHashAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.HasPasswordAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.SetTwoFactorEnabledAsync(`0,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.RestUserStoreBase`2.GetTwoFactorEnabledAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Mongoose.Common.Api.IMongooseApiController.DeferDisposal(System.IDisposable)">
            <summary>
            Registers an IDisposable for deferred disposal - item will be disposed when the Controller is disposed.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.IMongooseApiController.Request">
            <summary>
            Current HTTP request instance.  Property of any ApiController exposed here for access via extension methods.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.RestOAuthProviderBase`8">
            <inheritdoc />
            <typeparam name="TDataProvider">RestProviderBase subclass type.</typeparam>
            <typeparam name="THttpConfiguration">RestHttpConfigurationBase subclass type.</typeparam>
            <typeparam name="TUserStore">RestUserStoreBase subclass type.</typeparam>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
            <typeparam name="TSignInManager">RestSignInManagerBase subclass type.</typeparam>
            <typeparam name="TUserManager">RestUserManagerBase subclass type.</typeparam>
            <typeparam name="TOAuthProvider">The type name of the class which is subclassing this class.</typeparam>
        </member>
        <member name="M:Mongoose.Common.Api.RestOAuthProviderBase`8.#ctor(System.String,System.String)">
            <summary>
            Creates an instance of this class with default behavior
            </summary>
            <param name="publicClientId">Value for PublicClientId property.</param>
            <param name="cacheTenantId">Value for CacheTenantId property.</param>
        </member>
        <member name="P:Mongoose.Common.Api.RestOAuthProviderBase`8.CacheTenantId">
            <summary>
            InMemoryCache TenantID.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestOAuthProviderBase`8.GetUserManagerFromContextAndHydrateProperties(Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext)">
            <summary>
            Returns an instance of TUserManager derived from the OWin context which the endpoint was provisioned on.  Subclasses need to return this manager along with any properties fully hydrated.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestOAuthProviderBase`8.GetUserRequestingAuthentication(`6,Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext)">
            <summary>
            Returns the user requesting authentication
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestOAuthProviderBase`8.GrantResourceOwnerCredentials(Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext)">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.PerIpThrottlingPolicyRate.IpAddress">
            <summary>
            API key which will override the default rates with the rates set here.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.PerKeyThrottlingPolicyRate.ApiKey">
            <summary>
            API key which will override the default rates with the rates set here.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.RestProviderBase`8">
            <summary>
            Common base class for any REST provider.  Providers supply the structure which will be used at runtime to standup and execute an OWin self-hosted REST endpoint.
            </summary>
            <typeparam name="TDataProvider">The type name of the class which is subclassing this class.</typeparam>
            <typeparam name="THttpConfiguration">RestHttpConfigurationBase subclass type.</typeparam>
            <typeparam name="TUserStore">RestUserStoreBase subclass type.</typeparam>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
            <typeparam name="TSignInManager">RestSignInManagerBase subclass type.</typeparam>
            <typeparam name="TUserManager">RestUserManagerBase subclass type.</typeparam>
            <typeparam name="TOAuthProvider">RestOAuthProviderBase subclass type.</typeparam>
        </member>
        <member name="P:Mongoose.Common.Api.RestProviderBase`8.HttpConfiguration">
            <summary>
            HttpConfiguration for this provider.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.RestHttpConfigurationBase`8">
            <summary>
            HttpConfiguration base class for REST extension development.  Any properties or information needed at runtime by Authentication componenets or dat providers, should be expsed here for configuration.
            </summary>
            <typeparam name="TDataProvider">RestProviderBase subclass type.</typeparam>
            <typeparam name="THttpConfiguration">The type name of the class which is subclassing this class.</typeparam>
            <typeparam name="TUserStore">RestUserStoreBase subclass type.</typeparam>
            <typeparam name="TUser">Type of the user in the underlying system.</typeparam>
            <typeparam name="TUserId">Type of ID of TEwsRestUser.</typeparam>
            <typeparam name="TSignInManager">RestSignInManagerBase subclass type.</typeparam>
            <typeparam name="TUserManager">RestUserManagerBase subclass type.</typeparam>
            <typeparam name="TOAuthProvider">RestOAuthProviderBase subclass type.</typeparam>
        </member>
        <member name="P:Mongoose.Common.Api.RestHttpConfigurationBase`8.AccessTokenExpireTimeSpanMinutes">
            <summary>
            Number of minutes the access token will be good for.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestHttpConfigurationBase`8.TokenEndpointPath">
            <summary>
            Relative path which clients will use to authorize themselves.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.CreateUserManager(Owin.IAppBuilder)">
            <summary>
            Sub-classes need to provide the logic to defer UserManager creation into the app, and later Owin context
            </summary>
            <param name="app">IAppBuilder context for this current configuration</param>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.CreateSignInManager(Owin.IAppBuilder)">
            <summary>
            Sub-classes need to provide the logic to defer SignInManager creation into the app, and later Owin context
            </summary>
            <param name="app">IAppBuilder context for this current configuration</param>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.CreateOAuthProvider">
            <summary>
            Sub-classes need to provide the logic to defer OAuthProvider creation into the app, and later Owin context
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.ConfigureThrottling(System.Web.Http.HttpConfiguration)">
            <summary>
            Throttling configuration for all REST implementations.  This override is sealed to prevent circumventing throttling.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.RestHttpConfigurationBase`8.ServeSwaggerMetadata">
            <summary>
            If true, Swagger based metadata will be automatically served from the configured port.  If you do not wish to have your API self-documenting, set this property to false.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.ConfigureDocumentationProvider(System.Web.Http.HttpConfiguration)">
            <summary>
            Configures the Swagger documentation provider.  Override only if you need to further customize the Swagger page.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.RestHttpConfigurationBase`8.ConfigureRoutes(System.Web.Http.HttpConfiguration)">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Api.ThrottlingPolicy">
            <summary>
            Defines an API "throttling policy" which can be enforced on any REST API.
            </summary>
            <remarks>
            Used to configurate WebApiThrottle based policies.  See https://github.com/stefanprodan/WebApiThrottle
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicy.DefaultRates">
            <summary>
            Default throttling rate for all calls
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicy.WhiteListIpAddresses">
            <summary>
            Lists IP addresses which are exempt from throttling enforcement.  
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicy.ClientOverrides">
            <summary>
            Throttling overrides by API key.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicy.IpOverrides">
            <summary>
            Throttling overrides by IP address or range of IP
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicyRate.PerSecond">
            <summary>
            Number of requests allowed per second.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicyRate.PerMinute">
            <summary>
            Number of requests allowed per minute.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicyRate.PerHour">
            <summary>
            Number of requests allowed per hour.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicyRate.PerDay">
            <summary>
            Number of requests allowed per day.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.ThrottlingPolicyRate.PerWeek">
            <summary>
            Number of requests allowed per week.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.UrlFixerMiddleware">
            <summary>
            For some reason, when updating the OWIN NuGet packages, instead of needing to just 'double' encode URLs, we now need to triple encode for them to work!
            Of course, this is now a 'bug' instead of something we can document, as we would now be breaking compability, so we fix this with the below middleware.
            We basically take the original path of the URL, and add an extra encoding to it, so that it is now one more time encoded than it was before.
            In theory it would have already been double encoded. So we are triple encoding!
            Note to future dev: I tried fixing the fact that we even need to double encode with this middleware, but it wasn't possible.. because we receive a URL that has slashes pre-decoded in it..
            and there is no easy way to determine what the URL should be, and what the path is.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Api.CoreHttpConfigurationBase">
            <summary>
            Common HttpConfiguration base class for REST Extension development and internal HTTP Endpoint configuration (Portal and EWS Serve)
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.Name">
            <summary>
            Name for the HttpConfiguration.  Used when logging.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.ServeSwaggerMetadata">
            <summary>
            While only a simple bool, sub-classes must override this to ensure that for those never wishing Swagger to be supported, the proper ConfigurationIgnore attributes will be set.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.ThrottlingPolicy">
            <summary>
            Throttling policy which will be applied to the resulting endpoint
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.EndpointUrl">
            <summary>
            The actual endpoint that will be configured.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.CacheTenantId">
            <summary>
            InMemoryCache TenantID.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Api.CoreHttpConfigurationBase.AssembliesResolver">
            <summary>
            Sub-classes must return an instance of an IAssembliesResolver (you can use null for DefaultAssembliesResolver, CustomAssemblyResolver, or one of your design).
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.ConfigureAssemblyResolver(System.Web.Http.HttpConfiguration)">
            <summary>
            Applies AssembliesResolve to the supplied config.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.ConfigureDocumentationProvider(System.Web.Http.HttpConfiguration)">
            <summary>
            Override this method to configure a Swashbuckle/Swagger based docuemtation provider for your API.  See https://github.com/domaindrivendev/Swashbuckle for more information.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.XmlDocumentationFilesToInclude">
            <summary>
            Returns the list of all XML comment files to include when configuring the API documentation provider.  By default, we'll include the comment file for this sub-class'
            assembly (including all base classes) as long as the file actually exists.  If more (or less) are needed, override and return them.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.ConfigureRoutes(System.Web.Http.HttpConfiguration)">
            <summary>
            Configuring the routes is left to the sub-class.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.ConfigureSecurity(Owin.IAppBuilder)">
            <summary>
            Configuring the implementation of the AuthN/AuthR providers is left to the sub-class.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Api.CoreHttpConfigurationBase.ConfigureThrottling(System.Web.Http.HttpConfiguration)">
            <summary>
            Configuring the implementation of request throttling is left to the sub-class.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.CollectionLengthAttribute">
            <summary>
            Specifies the minimum and maximum number of items that can be in any IEnumerable.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.ConfigurationIgnoreAttribute">
            <summary>
            Attribute to mark a property on a Processor, or dependant property, as "not configurable".
            Used by the configuration extractor when scaffolding a ProcessConfiguration from a Processor.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.NotTraverseableAttribute">
            <summary>
            An attribute to explicitly declare a property as NOT traverseable when it would otherwise be traversable.
            </summary>
            <remarks>
            Required because of our use of Entity Framework's Parent 
            </remarks>
        </member>
        <member name="T:Mongoose.Common.Attributes.NotImmutableAttribute">
            <summary>
            Attribute to identify properties of a persisted entity which can be modifed by an API user.
            </summary>
            <remarks>
            I waffled a bit on whether to use Immutable or NotImmutable but I went with NotImmutable.  This forces the data class author to explicitly define what they are allowing for update.
            </remarks>
        </member>
        <member name="T:Mongoose.Common.Attributes.EncryptedStringAttribute">
            <summary>
            Specifies that the corresponding value will be encrypted when saved to the database.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.ConfigurationDefaultsAttribute">
            <summary>
            When attributed to a configurable subclass, newly created instances will automatically populate the Name and Description with the supplied values.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.RandomStringDefaultValueAttribute">
            <summary>
            Simarliy to DefaultValueAttribute however this attribute will generate a random value to a string property.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.RequiredDataPointListAttribute">
            <summary>
            Specialized validation attribute when the exact name and number of IDataPoint instances is known at design time.  
            </summary>
            <remarks>
            It is assumed that this is applied to a Reference Item property (eg ValueItemReader) with a child property (name equal childProperty eg DataPointsToRead) which is an IList of DataPoint.
            This will validate that that list has members which correspond to the list of names supplied at design time..
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Attributes.RequiredDataPointListAttribute.ChildProperty">
            <summary>
            The Property name which represents the IEnumerable of DataPoint
            </summary>
        </member>
        <member name="P:Mongoose.Common.Attributes.RequiredDataPointListAttribute.Names">
            <summary>
            The expected Names contained in ChildProperty
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.TooltipAttribute">
            <summary>
            Helper text which will be displayed to users of the Portal.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Attributes.TooltipAttribute.Value">
            <summary>
            The value of the tooltip
            </summary>
        </member>
        <member name="T:Mongoose.Common.Attributes.ValidUrlAttribute">
            <summary>
            Specialized validation attribute which validates that decorated properties represent a valid URL.  Unlike <see cref="T:System.ComponentModel.DataAnnotations.UrlAttribute"/>, this one recognizes localhost et Al.
            </summary>
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Attributes.VisibleWhenAttribute">
            <summary>
            Specifies that the parameter/ parameter set will be visible when the specified property is a specified value.
            Multiple VisibileWhenAttributes can be applied to a property, this will induce a logical OR between the attributes to determine visibility
            </summary>
        </member>
        <member name="P:Mongoose.Common.Attributes.VisibleWhenAttribute.PropertyName">
            <summary>
            The property to look at the value of
            </summary>
        </member>
        <member name="P:Mongoose.Common.Attributes.VisibleWhenAttribute.VisibleValues">
            <summary>
            The value the property must be to be visible
            </summary>
        </member>
        <member name="T:Mongoose.Common.CspEndpoint">
            <inheritdoc cref="T:Mongoose.Common.ICspEndpoint"/>>
        </member>
        <member name="P:Mongoose.Common.CspEndpoint.Domain">
            <inheritdoc />        
        </member>
        <member name="T:Mongoose.Common.Data.Encryptable`1">
            <summary>
            Generic ligthweight wrapper to support encryption and type safe marshalling of primitives and other known structs.
            </summary>
            <remarks>
            This is largely a wrapper which attempts to handle the conversion of T to a known type.  It is still the responsibility of consumer to call Decrypt and Encrypt on this instance supplying whatever encryption
            key is appropriate.  If Value is called when this instance is in an encrypted state an exception will be thrown.
            Supports any serializable Struct.
            You may also want to consider using SecureString as an alternative.
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Data.Encryptable`1.Value">
            <summary>
            The underlying value is accessed in an unencrypted manner.  An exception is thrown if this is in an encrypted state.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Encryptable`1.SerializableValue">
            <summary>
            Use Value to interact instead.  
            The underlying value serialized to a string.  This value may or may not be encyrpted depending on the state of the other properties.
            </summary>
            <remarks>
            The position in the class is important as logic depends on this being set AFTER IsEncrypted (as in when deserializing this).
            </remarks>
        </member>
        <member name="M:Mongoose.Common.Data.ExtensionMethods.Decrypt(Mongoose.Common.Data.IEncryptable,System.String)">
            <summary>
            Puts any IEncryptable into a decrypted state.
            </summary>
            <param name="encryptable">Instance to encrypt</param>
            <param name="encryptionKey">Encryption key</param>
        </member>
        <member name="M:Mongoose.Common.Data.ExtensionMethods.Encrypt(Mongoose.Common.Data.IEncryptable,System.String)">
            <summary>
            Puts any IEncryptable into the encrypted state.
            </summary>
            <param name="encryptable">Instance to encrypt</param>
            <param name="encryptionKey">Encryption key</param>
        </member>
        <member name="M:Mongoose.Common.Data.ExtensionMethods.FindOrCreateProcessorValue(Mongoose.Common.Data.IProcessorValueHelper,System.String,System.String,System.String,Mongoose.Common.DataPointValueType)">
            <summary>
            Returns a ProcessorValue instance by Key, Group and Aggregate value.  Assumes only one value will be returned.
            If the return is NULL, will create it and save it.  
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.ExtensionMethods.ActivateObject``1(Mongoose.Common.Data.IBindingInformation,System.Object[])">
            <summary>
            Instantiates an instance of the bindingInformation supplied.  Uses optional constructorParameters if supplied.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.ExtensionMethods.ActivateObject``1(Mongoose.Common.Data.IBindingInformation,System.Type@,System.Object[])">
            <summary>
            Instantiates an instance of the bindingInformation supplied.  Uses optional constructorParameters if supplied.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IBindingInformation.AssemblyFile">
            <summary>
            The relative or absolute path to the assembly DLL that contains the ClassName.
            </summary>
            <remarks>
            If relative pathing is used, then the assembly must be co-located with the runtime service binaries.
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Data.IBindingInformation.ClassName">
            <summary>
            The fully namspaced class name eg MyAssembly.SubNamespace.ClassName.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.IDataSource`1">
            <summary>
            Defines methods for a generic data source.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IDataSource`1.Items">
            <summary>
            Queryable list of items
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.IDataSource`1.Save">
            <summary>
            Save pending changes to the database
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.IDataSource`1.Add(`0)">
            <summary>
            Add a new instance to the database
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.IDataSource`1.Delete(`0)">
            <summary>
            Delete item from the database
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.IDataSource`1.Delete(System.Collections.Generic.IList{`0})">
            <summary>
            Delete all items from the database
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Mongoose.Common.Data.IDataSource`1.RevertChanges(`0)">
            <summary>
            Revert uncommited changes to the item
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.IEncryptable">
            <summary>
            Interface for any entity which, when persissted to the database, will be encrypted and when retrieved, will be decrypted.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IEncryptable.IsEncrypted">
            <summary>
            Indicates that the Value is encrypted.  
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IEncryptable.GetsEncrypted">
            <summary>
            Indicates that value will be encrypted when persisted to the database.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IEncryptable.Vector">
            <summary>
            When encrypted, this represents the InitializationVector for the encryption
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IEncryptable.EncryptedPropery">
            <summary>
            Provides the information on the property which is encrypted.
            </summary>
            <remarks>This could reprenet a performance hit.  Consider caching mechanisms in the implementation.</remarks>
        </member>
        <member name="T:Mongoose.Common.Data.IProcessorValueDataSource">
            <summary>
            IDataSource for ProcessorValue
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.IProcessorValueHelper.ProcessorValueSource">
            <summary>
            Data source to use to access ProcessorValues
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Licensing.ExtensionLicense">
            <summary>
            Data model for our persited Extension License.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.PublicId">
            <summary>
            Unique Id for this license.  
            </summary>
            <remarks>
            This should be globally unique and is generated by the license generator.  We persisit it for convenience later when we intend to report usage metrics.
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.AssemblyId">
            <summary>
            Exquivalent to the GUID on the Assembly which this is a license for.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.AssemblyName">
            <summary>
            Name of the Assembly.  e.g. Mongoose.Process.dll
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.AssemblyVersion">
            <summary>
            Version of the assembly which license should apply to in the form "major.minor.revision".
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.AssembleyPublicKeyToken">
            <summary>
            Optional value of the PublicKeyToke for a Strongly Named extension
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.LicensedTo">
            <summary>
            Optional information about who the license was issued to
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.InstallBefore">
            <summary>
            Optional DateTime where this license needs to be installed before.  
            Should be used with "Per Machine" deployments.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.Expires">
            <summary>
            Optional DateTime when the license will expire.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.Features">
            <summary>
            Extension author supplied features associated with this license
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.PublicKey">
            <summary>
            Public key supplied with the license to validate it at run time.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ExtensionLicense.RawLicense">
            <summary>
            Contents of the Elliptic Curve Digital Signature Algorithmus encrypted license file.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.Licensing.ExtensionLicense.FromLicenseFile(System.String)">
            <summary>
            Returns a new ExtensionLicense instance by parsing fileContents.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.Licensing.ExtensionLicense.CleanRawFile(System.String)">
            <summary>
            Files will have embedded CRLF escape characters and other escape sequeneces for having been processed through the browser.
            We'll attempt to scrub them as best as possible.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Licensing.IExtensionLicenseDataSource">
            <summary>
            IDataSource for ExtensionLicense
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.Licensing.ILicensable.IsLicensed">
            <summary>
            Indicates to the runtime framework whether the implementing entity requires a license.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.Licensing.ILicensable.ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense)">
            <summary>
            Custom feature license enforcement is up to the implementing extension.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.Licensing.ILicensable.GetType">
            <summary>
            Returns the type of the implementing class.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.ProcessorValue">
            <summary>
            Persistant Groupable Key/Value storage for Processors.
            </summary>
            <remarks>
            For now, this is global storage, ie all Processors can see all other data if it wishes.  API only allows modifying of the Value once an Value has been created.
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.Aggregate">
            <summary>
            When a Group is not sufficient, you can Aggreate Groups by adding this layer into a record.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.Group">
            <summary>
            Logical grouping of Key/Value pairs by a value, typically a unique value like a GUID or similar.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.Key">
            <summary>
            Key value into a Key/Value pairing (for a Group).
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.Value">
            <summary>
            Value in a Key/Value pairing (for a Group).
            </summary>
            <remarks>
            NOTE: Use SetValue extension methods to ensure that the Value is properly formatted for extraction.  This is especially true for DateTime ValueTypes.
            </remarks>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.ValueType">
            <summary>
            ValueType for the Value.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.ProcessorValue.IsReadOnly">
            <summary>
            Indicates that the Value cannot be changed by the API.  
            Changes made by a connection to the DbContext will always be allowed.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.ProcessorValueIdentifier">
            <summary>
            Simple abstraction of the three segments to uniquely identify.  
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.LateBoundDataType">
            <summary>
            Wrapper class for any late bound data type.  Late bound types are by definition not known at compile time and are "bound" or instantiated at run time using
            the Activator.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Data.LateBoundDataType.AssemblyFile">
            <inheritdoc />
        </member>
        <member name="P:Mongoose.Common.Data.LateBoundDataType.ClassName">
            <inheritdoc />
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.DaysOfMonth">
            <summary>
            Defines multiple "Days of the month" for pattern genration.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.DaysOfWeek">
            <summary>
            Used to define one or more "days of the week" for pattern genration.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.IntervalPattern">
            <summary>
            An interval pattern which supports Intervals in Seconds, Minutes, Hours, and Days
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.IntervalPatternBase">
            <summary>
            Common base class for a pattern based on an "interval".
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.MonthlyPattern">
            <summary>
            Schedules will run on selected months on either a DayOfMonth (eg 22nd) or WeekOfMonthDay (2nd Thursday) at the requested StartTime.  IntervalGap has no meaning.
            </summary>
        </member>
        <member name="F:Mongoose.Common.Data.Scheduling.MonthlyPatternType.DaysOfMonth">
            <summary>
            e.g. Monthly on the 2nd of the month
            </summary>
        </member>
        <member name="F:Mongoose.Common.Data.Scheduling.MonthlyPatternType.DaysOfWeekOfMonth">
            <summary>
            e.g. Monthly on the Third Wednesday of the month
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.MonthsOfYear">
            <summary>
            Defines multiple "months of a year" for pattern genration.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.PatternBase">
            <summary>
            Common base class for a pattern of occurences.
            </summary>
            <remarks>
            PatternBase must be ITraverseable in order to allow reflective synching of View/Model entities.  However, it is intentionally NOT IValidateableObject so that we don't valdidate it when it shouldn't be
            in the parent's context.  Therefore,  it is the parent's responsibility calling Validate inside it's Valdiate (IValidatableObject) implementation when it wants to conditionally validate the Pattern.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.Data.Scheduling.PatternBase.CalculateNextScheduledOccurrence(System.DateTime)">
            <summary>
            Returns the next DateTime instance from lastScheduledOccurence that falls on the pattern.  Return value should always be on the pattern.  Rounding of the value is to be expected.
            </summary>
            <param name="lastScheduledOccurrence"></param>
        </member>
        <member name="M:Mongoose.Common.Data.Scheduling.PatternBase.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            Its the responsibility of the outer most parent to call into Validate as needed.  See class remarks.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.WeeklyPattern">
            <summary>
            Used to define an "Weekly" recurrence pattern.  Occurrences will take place every IntervalGap days on the choosen DayOfWeek.
            </summary>
        </member>
        <member name="M:Mongoose.Common.Data.Scheduling.WeeklyPattern.IsInNextWeek(System.DateTime,System.DateTime)">
            <summary>
            Determines if dateToCheck falls in the week after startingPoint.  
            </summary>
        </member>
        <member name="T:Mongoose.Common.Data.Scheduling.WeeksOfMonth">
            <summary>
            Used to define an "n'th Day" in a month  (eg 2nd Tuesday) recurrence pattern.  
            </summary>
        </member>
        <member name="T:Mongoose.Common.DerivedFromConfigurationType">
            <summary>
            Enumeration of DerivedFromConfiguration subclasses.
            </summary>
        </member>
        <member name="F:Mongoose.Common.SettingCategory.Service">
            <summary>
            Public user configurable Service Settings
            </summary>
        </member>
        <member name="T:Mongoose.Common.ScheduleType">
            <summary>
            Identifies the reucurrence typf for the Schedule
            </summary>
        </member>
        <member name="M:Mongoose.Common.ExtensionMethods.SetValue``1(Mongoose.Common.IValuePoint,``0)">
            <summary>
            Native type setter for Value.  In addition to setting Value to a string representation of value, this method will also set ValueType based on T.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ExtensionMethods.GetValue(Mongoose.Common.IValuePoint,System.Boolean)">
            <summary>
            Returns the Value of this parameter as defined by ValueType property.  If the two do not jive the default value for the implied T is returned.
            If throwIsNotValid is true, then this method will throw an InvalidCastException.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ExtensionMethods.InferControllerName(Mongoose.Common.IMongooseController)">
            <summary>
            Infers the name of the controller from the name of the controller instance supplied.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ExtensionMethods.ExecuteAndLogCall``3(``0,System.String,``1,System.Func{``1,``2})">
            <summary>
            Helper method to log the EWS consume call occured, it's request object and response object and any error that may have occured.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ExtensionMethods.ValidateInstance(Mongoose.Common.IDerivedFromConfiguration)">
            <summary>
            Performs deep validation on this instance's object graph.  Validation is both attribute based (eg Required) and if then valid, semantic (eg IValidatableObject).
            In order to be validated, a child item of this class must be marked as ITraverseable or in an IEnumerable list of ITraverseable.
            </summary>
        </member>
        <member name="T:Mongoose.Common.GenericValidator">
            <summary>
            Leverages System.ComponentModel.DataAnnotations.Validator to perform schema and semantic validation on object.  The difference to Validator is that rather than only validating 
            the object supplied, we validate the entire object graph to a maximum as supplied or stipulated by ObjectExaminer.DefaultMaxDepth.
            </summary>
        </member>
        <member name="M:Mongoose.Common.GenericValidator.ValidateItem(SxL.Common.ITraversable)">
            <summary>
            Traverses an object graph and performs simple schema-level validation (fields required for data integretiy; field size etc) by using field meta-data (currently stored in Attributes).
            </summary>
        </member>
        <member name="M:Mongoose.Common.GenericValidator.AssertIsValidEnum``1(``0)">
            <summary>
            Asserts that the value for TEnum is one of the enumerated values.  Throws an ArgumentOutOfRangeException if it is not.
            </summary>
        </member>
        <member name="T:Mongoose.Common.IActionBroker">
            <summary>
            Helper interface to Start/Stop Configurations and/or EWS Server instances.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.StartConfiguration(System.Int32,Mongoose.Common.DerivedFromConfigurationType,System.String,System.String)">
            <summary>
            Create a start request for the requested Configuration.  Must supply credentials.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.StopConfiguration(System.Int32,Mongoose.Common.DerivedFromConfigurationType,System.String,System.String)">
            <summary>
            Create a stop request for the requested Configuration.  Must supply credentials.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.IsConfigurationRunning(System.Int32,Mongoose.Common.DerivedFromConfigurationType)">
            <summary>
            Returns true if the supplied Configuration is in fact executing.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.StartEwsServer(System.Int32,System.String,System.String)">
            <summary>
            Create a start request for the requested EWS Server.  Must supply credentials.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.StopEwsServer(System.Int32,System.String,System.String)">
            <summary>
            Create a stop request for the requested EWS Server.  Must supply credentials.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.IsServerRunning(System.Int32)">
            <summary>
            Returns true if the supplied EWS Server is in fact executing.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IActionBroker.IsServerRunning(System.String)">
            <summary>
            Returns true if the supplied EWS Server is in fact executing.
            </summary>
        </member>
        <member name="T:Mongoose.Common.ICspEndpoint">
            <summary>
            A CSP specific implementation of an IEndpoint.
            </summary>
            <inheritdoc cref="T:SxL.Common.IEndpoint"/>
        </member>
        <member name="P:Mongoose.Common.ICspEndpoint.Domain">
            <summary>
            The Domain to connect to.  (Optional)
            </summary>
        </member>
        <member name="T:Mongoose.Common.ICache">
            <summary>
            Cross thread accessable in memory cache.  Everything, including reference types, are serialized into the cache.  This means that an AddItem followed by a RetrieveItem will
            never return the same instance of item.  This is intentional to prevent cross thread access issues.  All methods lock the internal storage for the duration of the call.
            Data can be isolated within logical "tenant" silos by using the tenantId parameter for all calls.  If no tenantId is used, the data can be accessed by anyone with access to the cache.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ICache.AddOrUpdateItem``1(``0,System.String,System.Boolean,System.String,System.Int32)">
            <summary>
            Adds item to the cache with the supplied key.
            </summary>
            <param name="item">Value of the item to update the cache with.</param>
            <param name="key">Key of the value.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
            <param name="expiresInMinutes">Expiration time in minutes for this.  Use 0 for never expires. Default is 60.</param>
            <param name="isVisibleInViewer">Whether or not this cache item will be visible in the InMemoryCache viewer in the portal. Default is true.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.AddOrUpdateItem``1(``0,System.String,System.String,System.Int32)">
            <summary>
            Adds item to the cache with the supplied key. By default this value will be visible in the InMemoryCache viewer.
            </summary>
            <param name="item">Value of the item to update the cache with.</param>
            <param name="key">Key of the value.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
            <param name="expiresInMinutes">Expiration time in minutes for this.  Use 0 for never expires. Default is 60.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.DeleteItem(System.String,System.String)">
            <summary>
            Removes the item with the supplied key from the cache.
            </summary>
            <param name="key">Key of the value.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.RetrieveItem``1(System.String,System.Boolean,System.Func{``0},System.String,System.Int32)">
            <summary>
            Returns an item from cache as T.  
            </summary>
            <param name="key">Key of the value.</param>
            <param name="seedValue">Optional delegate to seed the value if the item is NOT in the cache.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
            <param name="expiresInMinutes">Expiration time in minutes for this.  Use 0 for never expires.  Default is 60.</param>
            <param name="isVisibleInViewer">Whether or not this cache item will be visible in the InMemoryCache viewer in the portal. Default is true. Only valid if seedValue is not null</param>
        </member>
        <member name="M:Mongoose.Common.ICache.RetrieveItem``1(System.String,System.Func{``0},System.String,System.Int32)">
            <summary>
            Returns an item from cache as T.  If a seed value is included, this value will be visible in the InMemoryCache viewer in the portal.
            </summary>
            <param name="key">Key of the value.</param>
            <param name="seedValue">Optional delegate to seed the value if the item is NOT in the cache.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
            <param name="expiresInMinutes">Expiration time in minutes for this.  Use 0 for never expires.  Default is 60.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.RetrieveItem(System.String,System.String)">
            <summary>
            Returns an item from cache dynamically as the type it was cached under.  If the item is NOT in the cache NULL will be returned.  
            If you are attempting to access a non-reference type or Nullable struct, you MUST use the generic overload for this method and optionally supply a seeding delegate.
            </summary>
            <param name="key">Key of the value.</param>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.Clear(System.String)">
            <summary>
            Clears all contents from the cache.
            </summary>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.Keys(System.String)">
            <summary>
            Returns the keys currently in use.  
            </summary>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
        </member>
        <member name="M:Mongoose.Common.ICache.DeleteTenant(System.String)">
            <summary>
            Deletes the entire cache for tenantId.
            </summary>
            <param name="tenantId">Id of the Tenant within the cache where data is stored.</param>
        </member>
        <member name="T:Mongoose.Common.IDerivedFromConfiguration">
            <summary>
            The public common interface for an entity that is dervied from a ConfigurationBase subclass.
            </summary>
        </member>
        <member name="P:Mongoose.Common.IDerivedFromConfiguration.Name">
            <summary>
            Name of the derived item.
            </summary>
        </member>
        <member name="P:Mongoose.Common.IDerivedFromConfiguration.ConfigurationId">
            <summary>
            Id of the Configuration which sourced the item.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IDerivedFromConfiguration.OnValidateInstanceCompleted(System.Collections.Generic.List{Mongoose.Common.Prompt})">
            <summary>
            Allows sub-classes early access to the prompts.  
            </summary>
        </member>
        <member name="T:Mongoose.Common.IMongooseController">
            <summary>
            Interface for any controller in the solution
            </summary>
        </member>
        <member name="P:Mongoose.Common.IMongooseController.ControllerName">
            <summary>
            Controller name used for logging.
            </summary>
        </member>
        <member name="P:Mongoose.Common.IMongooseController.LoggingCategory">
            <summary>
            Category all actions will be logged under
            </summary>
        </member>
        <member name="T:Mongoose.Common.IProcessor">
            <summary>
            The public common interface for Processor.
            </summary>
        </member>
        <member name="T:Mongoose.Common.IRestProvider">
            <summary>
            The public common interface for RestProvider.
            </summary>
        </member>
        <member name="P:Mongoose.Common.IRestProvider.Endpoint">
            <summary>
            The root endpoint which will be provisioned.
            </summary>
        </member>
        <member name="M:Mongoose.Common.IRestProvider.GetHttpConfiguration">
            <summary>
            Returns the HttpConfiguration subclass that is being using used by the implementing class.
            </summary>
        </member>
        <member name="T:Mongoose.Common.LogCategory">
            <summary>
            Enumerates categories used by Mongoose.
            </summary>
            <remarks>
            Not modelled as a straight enumeration so that consumers do not need to use ToString() on every usage
            </remarks>
        </member>
        <member name="T:Mongoose.Common.MongooseObjectFactory">
            <summary>
            Singleton StructureMap Container wrapper for IoC decependency injection.  
            </summary>
            <remarks>
            StructureMap, the Inversion of Control container used in this solution has depracated ObjectFactory and now recommends usage of the Container class.  
            I can certainly understand the desire to move towards more short lived, managed Container instances however, for this solution, the ObjectFactory pattern was a perfect fit.  
            Since we cannot rely on StructureMap.ObjectFactory being present moving forward, this class will mimic it's spirit by providing a Singleton instance of a type factory.
            Configuration is done as it was with ObjectFactory via a Configure call.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.MongooseObjectFactory.GetInstance``1">
            <summary>
            Returns an instance of the type T as defined by the configured IoC container.  If the container has not been configured, this method will return null.
            </summary>
        </member>
        <member name="M:Mongoose.Common.MongooseObjectFactory.Configure(System.Action{StructureMap.ConfigurationExpression})">
            <summary>
            Used to add additional configuration to a Container *after* the initialization.
            </summary>
        </member>
        <member name="M:Mongoose.Common.MongooseObjectFactory.ConfigureDataDirectory">
            <summary>
            Initializes the current application's DataDirectory for logging and local DB configurations.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetClientSafeTypeName(System.Type)">
            <summary>
            Fix for a load balancing issue where certain fully qualified type names are not the same on both web servers.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetTypeFromClientSafeTypeName(System.String)">
            <summary>
            Currently this is just provided for naming neatness.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetTypeFromAssemblyQualifiedName(System.String)">
            <summary>
            Found that we were doing AssemblyQualifiedName -> Type in a few areas, plus I wanted to try out caching of the results.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetObjectMemberValue(System.Object,System.Reflection.MemberInfo)">
            <summary>
            Helper routine to obtain the value of a member on an object.  Only intended for use on properties and fields.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.SetObjectMemberValue(System.Object,System.Reflection.MemberInfo,System.Object)">
            <summary>
            Helper routine to set the value of a member on an object.  Only intended for use on properties and fields.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetFieldPropMemberType(System.Reflection.MemberInfo)">
            <summary>
            Gets the underlying type of a Field/Property member.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.TypeIsA(System.Type,Mongoose.Common.BaseTypes)">
            <summary>
            Encapsulates some of the complexities of checking whether a Type derives from a particular class
            of implements a particular interface
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.DoesTypeHaveInterface(System.Type,System.Type)">
            <summary>
            Returns true if a given type implements interfaceType
            </summary>
            <remarks>
            As part of perf enhancement, do NOT pass interfaceType with []; eg: the call should like: DoesTypeHaveInterface(mytestType, typeof(ICollection)).
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.ListMembersWithThisAttributeType``1(System.Type)">
            <summary>
            Given a type, returns each property on that type which is tagged with one or more attributes of the given type  (or any of its subclasses).
            Looks at the schema only, not the underlying values.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.ListMembersWhichAreThisConcreteType(System.Type,System.Type)">
            <summary>
            Given a type, returns each property on that type which is of type "memberConcreteType".
            Does not currently check for subclasses (create an overload if you need that).
            Looks at the schema only, not the underlying values.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.ForEachReadWriteProperty``1(System.Type,System.String,Mongoose.Common.ObjectExaminer.ForEachReadWritePropertyDelegate{``0})">
            <summary>
            Helper which executes a delegate for each R/W PropertyInfo on a given a Type.
            Delegate will perform some filtering, and return an object to be added to the results, or return null,
            indicating that we can move on to the next PropertyInfo w/o changing the results.
            
            The delegate will typically return the PropertyInfo itself, functioning as a simple filter,
            but in other cases we'll return a Pair[PropertyInfo, T] as a "filter plus returning info
            we had handy while we already had the reflective info torn apart".  Ex: ListFieldLevelSecurityMembers.
            
            Looks at the schema only, never actual values.  This allows us to cache results for a particular Type,
            which gives a huge performance gain (see remarks).
            </summary>
            <remarks>
            Notes on Performance Tuning of ListMembersDeclaredAsTraversable - JRK - 07/24/06:
            - Measured w/ CompuWare running for 1800s in JobsByServDate test
            - Called 18,791 times; avg w/ children = 7.9s; accounts for 8.9% of total runtime
            - W/o profiler attached - runtime 371s, but sometimes as low as 270s; sporadic :(
            - Testing the following tweaks using ListMembersDeclaredAsTraversable_PerformanceTest w/ 20K interations; 
                comparing old vs new side-by-side.  Readings tend to vary run-by-run a little, so in many cases I'm
                just trying to ensure we don't make things worse EVER, and we SOMETIMES make them better.
            
            - Switched GetProperties() to GetProperties(BindingFlags.Instance | BindingFlags.Public), and
            - Switched from GetSetter to CanWrite, which at least by name, seems more direct.  
            + Minor gain (20s -> 19.2s)
            
            - Changed memberType.Name != "String"/"System.String" to typeof(string), since the link above says (vaguely) that Name
                is slow, and I know typeof() is reasonably fast.
            + Minor gain (17s -> 15s) (net 1s since this is applied on top of above changes).
            
            - Complete cache of inputs/outputs, using full Type and MemberInfo objects (no Handles).
            + HUGE gain (12.7s -> 31ms) - Interesting, maybe having the reference to the MemberInfo in the cache actually sped up
                access even in the old code, which didn't access the cache directly?
            + Run of JobsByServDate test (although sporadic both before and after) seems to reflect improvement - 207s, 221s.
            + Profiled run seems to reflect improvement - avg w/ children = 633ms; accounts for 0.5% of total runtime.
            
            + Other candidates for future tweaks here, or in other reflection areas:
                - http://msdn.microsoft.com/msdnmag/issues/05/07/Reflection/
                    // Obtaining a Handle from an MemberInfo
                    RuntimeMethodHandle handle = typeof(D).GetMethod("MyMethod").MethodHandle;
                    // Resolving the Handle back to the MemberInfo
                    MethodBase mb = MethodInfo.GetMethodFromHandle(handle);
                    Also: DynamicMethod section
                - How often is our TypeIsA() method called?
                - Read up on TypeDescriptior (UI only (prop grid) thing?)
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.ForEachReadWriteProperty_FromCache(System.String)">
            <summary>
            Returns null if data is not cached.
            </summary>
            <remarks>
            Our first try is at caching the full MemberInfo.  As described here - http://msdn.microsoft.com/msdnmag/issues/05/07/Reflection/
            this has the possible downside that we will keep MemberInfo alive in the GC Heap forever.  My plan is to try this first,
            since we use the info so much anyway...  If it becomes a problem, we could alternatively cache only the MethodHandle structures,
            and reassemble the full MethodInfo in this method instead.
            
            Also, our first try is to key our cache on the entire Type object, since "Name property" (ambiguous) is listed under the known 
            slow methods in the above article's sidebar.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.SaveForEachReadWritePropertyCache(System.String,System.Object)">
            <summary>
            Caches the results.
            </summary>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.ListMembersDeclaredAsTraversable(SxL.Common.ITraversable)">
            <summary>
            Given a node, defines which properties/fields on that object give access to owned sub-entities based purely on their declaration.
            In other words, we do not look at the contents of the props/fields; we only look at the MemberInfo.  If node.ChildItem
            is null, we still return it as traversable.  This behavior is required so that we can use this function in the loading
            of new entities and child entities.
            </summary>
            <param name="node"></param>
            <returns>ArrayList of MemberInfo</returns>
            <remarks>
            This "declaration-only" approach is a result of refactoring I did for use with ORM-loading, where we don't have runtime types available
            and must work only with declarations.  
            </remarks>
        </member>
        <member name="T:Mongoose.Common.ObjectExaminer.ProcessObjectDelegate">
            <summary>
            Delegate for use in Object Graph Traversal.  Called once for each node object encountered.
            See TraverseObject
            </summary>
            <remarks>
            Return FALSE if you do not want that the given node's members to be followed (traversed) in the graph.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.TraverseObject(SxL.Common.ITraversable,SxL.Common.ITraversable,System.Int32,System.Int32,Mongoose.Common.ObjectExaminer.ProcessObjectDelegate)">
            <summary>
            Controls flow through the object graph for things like validation and security rights.
            </summary>
            <param name="node"> </param>
            <param name="currentDepth"></param>
            <param name="parentNode"> </param>
            <param name="maxDepth">Zero will process only the single object passed.  Greater than zero will process additional levels of related objects.</param>
            <param name="callBack">Delegate to a worker function which will be called for each node (object) in the graph.</param>
            <remarks>
            Since we don't have strongly-formed metadata on object relations, this routine currently uses reflection over an object's 
            Properties and Fields (member variables) to define a graph (typically a tree) of related objects.  To eliminate a lot of
            unneccessary objects from the tree, we require nodes to implement ITraversable.
            
            Future enhancements might include:
            	- Ideally: reading (and caching) strictly-formed metadata from an XML file
                * Example of using Reflection.Emit() instead of reflection - http://www.codeproject.com/useritems/ORMReflectionEmit.asp
            	- Or: ITraversable interface has GetChildObjects() which returns an ArrayList of direct children
            	- Or maybe: allow caller additional control over paths taken vs. ignored by passing strings ("ignore Solution.Categorys.Parent")
            	- Or at least: create modes/overloads to handle specific paths outside of the orignal ones I built centered on Validation and Security
                * For performance, remove recursion, ex: http://blogs.msdn.com/toub/archive/2004/10/29/249858.aspx
            	
            Also, there is currently no logic here to prevent cyclical relationships from being processed twice!
            
            JRK - 12/08/05 - Removed public User input in favor of RequestContext.  Will keep explicit user passing internally for now.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetTraversableItems(SxL.Common.ITraversable)">
            <summary>
            Returns all Traversable Nodes, INCLUDING the parent input node.
            This is useful when you don't care much about the *context* of
            traversal and don't need to evaluate specific parent/child pairs.
            </summary>
            <param name="startingNode">The node from which traversal starts.  This node is NOT included in the results.</param>
            <remarks>
            I considered using C# 2.0 iterators - http://msdn.microsoft.com/vcsharp/2005/overview/language/iterators/
            or some other variation on IEnumerable, but neither of those maps well to our existing TraverseObject
            code.  Iterators would need a sister method with "yield return" statements instead of callbacks.
            IEnumerable would need a custom IEnumerator written from scratch to maintain "current position"
            in the tree.  So for now Ockham's razor nixes them both in favor of manual ArrayList assembly.
            
            JRK - 12/08/05 - Remove public User input in favor of RequestContext.  Will keep explicit user passing internally for now.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.GetTraversableItems(System.Collections.IEnumerable)">
            <summary>
            Overload (slightly) optimized for traversing starting at an Enumerable.
            </summary>
            <remarks>
            Performance gain is probably minimal over calling the singular version multiple times.  
            The only obvious gain is in the single setup/tear-down of the worker object.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.ObjectExaminer.IsValueType(System.Type)">
            <summary>
            Returns true if the type is a value type and Yes "String" and "int" are value types.
            </summary>
        </member>
        <member name="T:Mongoose.Common.BaseTypes">
            <summary>
            Defines diffent types by primitive value.
            </summary>
            <remarks>
            See ObjectExaminer.TypeIsA
            </remarks>
        </member>
        <member name="T:Mongoose.Common.Prompt">
            <summary>
            A combination of a message and a severity to be communicated upstream.  Interpretation of the severity is up to the calling code.
            </summary>
        </member>
        <member name="T:Mongoose.Common.Resources.Prompts">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.ChangeNotAllowed_Description">
            <summary>
              Looks up a localized string similar to Property {0} cannot be modified..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.CollectionMinPrompt">
            <summary>
              Looks up a localized string similar to {0} must contain at least {1} item(s)..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.CollectionOutOfRangePrompt">
            <summary>
              Looks up a localized string similar to {0} must contain between {1} and {2} item(s)..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.EntityNotFound_Description">
            <summary>
              Looks up a localized string similar to {0} {2} {1} not found.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.ExpectedItemNotFound">
            <summary>
              Looks up a localized string similar to {0} does not contain the expected item &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.IncorrectPointCount">
            <summary>
              Looks up a localized string similar to {0} does not contain the expected number of items ({1})..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.IncorretPointCount">
            <summary>
              Looks up a localized string similar to {0} does not contain the expected number of items ({1})..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.InvalidRangePrompt">
            <summary>
              Looks up a localized string similar to {0} must be in the range of {1} to {2}..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.InvalidRequest">
            <summary>
              Looks up a localized string similar to Invalid request.  {0}.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.InvalidUrl">
            <summary>
              Looks up a localized string similar to {0} is not a valid fully-qualified http or https URL..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.LicenseValidatorNotFound">
            <summary>
              Looks up a localized string similar to No license validator has been configured..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NoDayOfMonthSelected">
            <summary>
              Looks up a localized string similar to At least one day of the month must be selected..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NoDayOfWeekSelected">
            <summary>
              Looks up a localized string similar to At least one day of the week must be selected..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NoDefaultThrottlingPolicy">
            <summary>
              Looks up a localized string similar to No default throttling policy has been defined..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NoMonthsOfYearSelected">
            <summary>
              Looks up a localized string similar to At least one month of the year must be selected..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NotFieldOrPropertyMember">
            <summary>
              Looks up a localized string similar to Member is not a Field or Property member..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.NoWeekOfMonthSelected">
            <summary>
              Looks up a localized string similar to At least one week of the month must be selected..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.OneDefaultPolicyAllowed">
            <summary>
              Looks up a localized string similar to Only one default throttling policy can be defined..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.PropertyNotFound">
            <summary>
              Looks up a localized string similar to The expected property {0} is not present and cannot be validated..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.RangeError">
            <summary>
              Looks up a localized string similar to Value must be between {0} and {1}..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.RequiredFieldPrompt">
            <summary>
              Looks up a localized string similar to The {0} field is required..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.UnknownUser">
            <summary>
              Looks up a localized string similar to Unknown.
            </summary>
        </member>
        <member name="P:Mongoose.Common.Resources.Prompts.ValueDataTypeInconsistent">
            <summary>
              Looks up a localized string similar to The Value is not consistent with the specified type of {0}..
            </summary>
        </member>
        <member name="P:Mongoose.Common.Security.BearerToken.TokenUri">
            <summary>
            The complete URI used to obtain the token
            </summary>
        </member>
        <member name="M:Mongoose.Common.Security.BearerToken.ObtainToken(System.Uri,System.String,System.String,System.String)">
            <summary>
            Requests a Bearer Token from baseAddress/tokenActionName and returns it.
            </summary>
            <param name="baseAddress">The servers base address (do not include the route here).</param>
            <param name="username">Username to authenticate with.</param>
            <param name="password">Password to authenticate with.</param>
            <param name="tokenActionName"></param>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.ObjectToXml(System.Object,System.Type)">
            <summary>
            Converts item into a XML snippet suitable for persistance as XML
            </summary>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.XmlToObject(System.String,System.Type,System.Object)">
            <summary>
            Converts XML into an object of type T.  If string is empty, will return returnIfUnset.       
            </summary>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.XmlToObject(System.String,System.Type)">
            <summary>
            Converts XML into an object of type T.  If string is empty, will return null.        
            </summary>
            <remarks>
            See also ObjectToXmlForSql
            </remarks>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.XmlToObject``1(System.String,``0)">
            <summary>
            Converts xml into an object of type T.  If string is null/empty, will return the given default value.
            </summary>
            <remarks>
            See also ObjectToXmlForSql.
            </remarks>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.XmlToObject``1(System.String)">
            <summary>
            Converts XML into an object of type T.  If string is empty, will return default(T).
            </summary>
            <remarks>
            See also ObjectToXmlForSql
            </remarks>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.GetXmlSerializer(System.Type)">
            <summary>
            Gets an XmlSerializer for the type provided.  For optimization, we cache the serializer for user
            </summary>
        </member>
        <member name="M:Mongoose.Common.SerializationHelpers.GetTypeNameForXmlTag(System.Type)">
            <summary>
            Returns the type name as it will appear in XML.
            This is for generics, where Class[T] shows up as ClassOfT in XML.
            </summary>
        </member>
        <member name="T:Mongoose.Common.TemporaryPrincipal">
            <summary>
            Intended to be used in a using block when you wish to temporarilty change the current Thread principal.  When the using block closes, this will
            automatically set the principal back to what it was.
            </summary>
        </member>
    </members>
</doc>
