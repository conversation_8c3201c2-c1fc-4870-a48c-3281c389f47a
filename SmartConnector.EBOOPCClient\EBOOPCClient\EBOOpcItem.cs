﻿using System;
using System.Runtime.InteropServices;
using OPCDA.NET;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x02000007 RID: 7
	public class EBOOpcItem
	{
		// Token: 0x0600001F RID: 31 RVA: 0x00003AB0 File Offset: 0x00001CB0
		public EBOOpcItem()
		{
		}

		// Token: 0x06000020 RID: 32 RVA: 0x00003B18 File Offset: 0x00001D18
		public EBOOpcItem(string itemID, string itemPath, bool active, int hClient, VarEnum vt, int address, string strGroupName, string strEWSItemID)
		{
			this._opcItemDef.ItemID = itemID;
			if (!string.IsNullOrEmpty(itemPath))
			{
				this._AccessPath = itemPath;
			}
			if (!string.IsNullOrEmpty(strEWSItemID))
			{
				this._EWSItemID = strEWSItemID;
			}
			this._opcItemDef.Active = active;
			this._opcItemDef.HandleClient = hClient;
			this._opcItemDef.RequestedDataType = vt;
			this._GroupName = strGroupName;
			this._Address = address;
		}

		// Token: 0x04000013 RID: 19
		public OPCItemDef _opcItemDef = new OPCItemDef();

		// Token: 0x04000014 RID: 20
		public OPCItemResult _opcItemRes = new OPCItemResult();

		// Token: 0x04000015 RID: 21
		public OPCItemState _opcItemState = new OPCItemState();

		// Token: 0x04000016 RID: 22
		public string _GroupName = string.Empty;

		// Token: 0x04000017 RID: 23
		public string _AccessPath = string.Empty;

		// Token: 0x04000018 RID: 24
		public string _EWSItemID = string.Empty;

		// Token: 0x04000019 RID: 25
		public string _DataTypeFromUCME = "string";

		// Token: 0x0400001A RID: 26
		public int _NumOfItemsInArray = 1;

		// Token: 0x0400001B RID: 27
		public int _Address;

		// Token: 0x0400001C RID: 28
		public bool _HasChange;
	}
}
