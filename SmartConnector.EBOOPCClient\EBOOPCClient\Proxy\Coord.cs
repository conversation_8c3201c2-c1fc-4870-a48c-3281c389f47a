﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000010 RID: 16
	public class Coord
	{
		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000085 RID: 133 RVA: 0x0000817E File Offset: 0x0000637E
		// (set) Token: 0x06000086 RID: 134 RVA: 0x00008186 File Offset: 0x00006386
		[JsonProperty("lat")]
		public double Lat { get; set; }

		// Token: 0x17000017 RID: 23
		// (get) Token: 0x06000087 RID: 135 RVA: 0x0000818F File Offset: 0x0000638F
		// (set) Token: 0x06000088 RID: 136 RVA: 0x00008197 File Offset: 0x00006397
		[JsonProperty("lon")]
		public double Lon { get; set; }
	}
}
