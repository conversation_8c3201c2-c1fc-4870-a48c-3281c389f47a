using System;
using System.Runtime.InteropServices;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	/// <summary>
	/// License checking functionality for UCME (SmartConnector EBO OPC Client)
	/// </summary>
	public static class UCMECheckLicense
	{
		/// <summary>
		/// Check license validity and return license information
		/// </summary>
		/// <param name="licInfo">Output parameter containing license information</param>
		/// <returns>0 if license is valid, -1 if invalid or error occurred</returns>
		public static int CheckLicense(out UCMELicInfo licInfo)
		{
			licInfo = new UCMELicInfo();
			try
			{
				int day = 0;
				int month = 0;
				int year = 0;
				int version = 0;
				
				// Call the native CheckLicense.dll function
				if (CheckLicense(out day, out month, out year, out version) == 0 && version >= Global.CURRENT_VERSION_NUMBER)
				{
					licInfo.m_nVer = version;
					
					// Check for unlimited license (indicated by -1 values)
					if (day == -1 && month == -1 && year == -1)
					{
						licInfo.m_ExpertionUnlimited = true;
						licInfo.m_bDemo = false;
					}
					
					// Check for time-limited license
					if (day > 0 && month > 0 && year > 0)
					{
						// Handle 2-digit years by adding 2000
						if (year < 100)
						{
							year += 2000;
						}
						
						// Add one day to the expiration date
						TimeSpan oneDay = new TimeSpan(1, 0, 0, 0);
						licInfo.m_dtExpierd = new DateTime(year, month, day);
						licInfo.m_dtExpierd += oneDay;
						licInfo.m_ExpertionUnlimited = false;
						
						// Check if license is still valid
						if (DateTime.Now < licInfo.m_dtExpierd)
						{
							licInfo.m_bDemo = false;
						}
					}
					
					return 0;
				}
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "Check License fail, error '{0}'", new object[]
				{
					ex.Message
				});
				return -1;
			}
			return -1;
		}

		/// <summary>
		/// Native function import from CheckLicense.dll
		/// </summary>
		/// <param name="dd">Day of expiration</param>
		/// <param name="mm">Month of expiration</param>
		/// <param name="yy">Year of expiration</param>
		/// <param name="ver">License version</param>
		/// <returns>0 if successful, non-zero if failed</returns>
		[DllImport("CheckLicense.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
		public static extern int CheckLicense(out int dd, out int mm, out int yy, out int ver);

		/// <summary>
		/// License information structure
		/// </summary>
		public class UCMELicInfo
		{
			/// <summary>
			/// License expiration date
			/// </summary>
			public DateTime m_dtExpierd;

			/// <summary>
			/// True if license has unlimited expiration
			/// </summary>
			public bool m_ExpertionUnlimited;

			/// <summary>
			/// License version number
			/// </summary>
			public int m_nVer = -1;

			/// <summary>
			/// True if running in demo mode (default)
			/// </summary>
			public bool m_bDemo = true;
		}
	}
}
