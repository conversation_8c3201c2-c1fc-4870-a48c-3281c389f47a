<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ews.Server.Contract</name>
    </assembly>
    <members>
        <member name="P:Ews.Server.Contract.DataExchangeBase`3.UsedNameSpace">
            <summary>
            Namespace which will be used
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.DataExchangeBase`3.SupportedMethodNames">
            <summary>
            List of methods that are currently supported by this Serve implementation.
            </summary>
            <remarks>
            It is possible that the implementing class makes this variable based on the current principal but that is not the intention of this.
            </remarks>
        </member>
        <member name="P:Ews.Server.Contract.DataExchangeBase`3.SupportedAlarmEventTypes">
            <summary>
            List of AlarmEventTypes supported by this Serve implementation.
            </summary>
        </member>
        <member name="T:Ews.Server.Contract.Processor.PagedResultsProcessor`5">
            <summary>
            Base class for any processor which supports pagaing
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.PagedResultsProcessor`5.ConvertToListOfEwsData(System.Linq.IQueryable{`3})">
            <summary>
            Sub-classes need to implement the conversion.  
            
            NOTE: Beware of "LINQ to Entities does not recognize the method..." type errors!  pageOfData is already the subset of data so do ToList() first.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.PagedResultsProcessor`5.PushDataOntoResponse(`1,System.Collections.Generic.List{`2},System.Boolean,System.Int32,System.Boolean)">
            <summary>
            Subclasses should push the pagedData onto the appropriate response object and handle any and all paging issues based on the supplied information.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.PagedResultsProcessor`5.ExtractPagingData(System.String)">
            <summary>
            Subclasses need to extract cached "paging data" based on the supplied moreDataRef parameter.
            Optionally, subclasses can overwrite/re-hydrate any filter parameters which were cached with it.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.PagingData.MoreDataRef">
            <summary>
            MoreDataRef or key this paging information was cached under
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.PagingData.NextPage">
            <summary>
            The next logical page for the filtered data set.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.AcknowledgeAlarmEventsProcessor.IsAcknowledged(System.String)">
            <summary>
            Returns true if the AlarmItem represented by id is in the Acknowledged state
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.AcknowledgeAlarmEventsProcessor.IsAcknowledgeable(System.String)">
            <summary>
            Returns true if the AlarmItem represented by id is Acknowledgeable
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.ForceValuesProcessor.IsValidValue(System.String,System.String,Ews.Server.Contract.ResultType@)">
            <summary>
            Main entry point for value validation.  Validates that the ValueItem exists and that the value is consistant with the ValueItem.  Override overloaded method for implementation specific 
            value validation.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.ForceValuesProcessor.IsValidValue(System.String,System.String)">
            <summary>
            Validates the supplied value for the id given.  Test for datatype inconsistancies.  
            At this level, this method will always return true.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmEventsProcessor`1.PriorityFrom">
            <summary>
            Minimum Priority to return.  If not supplied, the default is 0.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmEventsProcessor`1.PriorityTo">
            <summary>
            Maximum Priority to return.  If not supplied, the default is 1000.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmEventsProcessor`1.Types">
            <summary>
            LinkedList of Types derived from the request and set during ValidateRequest.  NULL if none are supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmHistoryProcessor`1.PriorityFrom">
            <summary>
            Minimum Priority to return.  If not supplied, the default is 0.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmHistoryProcessor`1.PriorityTo">
            <summary>
            Maximum Priority to return.  If not supplied, the default is 1000.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmHistoryProcessor`1.TimeFrom">
            <summary>
            Defaults to one month ago if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmHistoryProcessor`1.TimeTo">
            <summary>
            Defaults to now if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetAlarmHistoryProcessor`1.Types">
            <summary>
            LinkedList of Types derived from the request and set during ValidateRequest.  NULL if none are supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoricalDataAggregationProcessor.TimeFrom">
            <summary>
            Defaults to one month ago if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoricalDataAggregationProcessor.TimeTo">
            <summary>
            Defaults to now if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoricalDataAggregationProcessor.Group">
            <summary>
            Aggregation Group extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoricalDataAggregationProcessor.Type">
            <summary>
            Aggregation Type extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoricalDataAggregationProcessor.Periods">
            <summary>
            Aggregation Periods extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoryProcessor`1.TimeFrom">
            <summary>
            Defaults to one month ago if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetHistoryProcessor`1.TimeTo">
            <summary>
            Defaults to now if not supplied.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.GetNotificationProcessor`1.GetSubscriptionStatus(System.String)">
            <summary>
            Returns the status of the supplied Subscription.  If the subscription is not found or the subscriptionId is invalid, return Invalid.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetSystemEventsProcessor.TimeFrom">
            <summary>
            Defaults to one month ago if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetSystemEventsProcessor.TimeTo">
            <summary>
            Defaults to now if not supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetSystemEventsProcessor.PriorityFrom">
            <summary>
            Minimum Priority to return.  If not supplied, the default is 0.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetSystemEventsProcessor.PriorityTo">
            <summary>
            Maximum Priority to return.  If not supplied, the default is 1000.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetSystemEventsProcessor.Types">
            <summary>
            LinkedList of Types derived from the request and set during ValidateRequest.  NULL if none are supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetUpdatedAlarmEventsProcessor`1.PriorityFrom">
            <summary>
            Minimum Priority to return.  If not supplied, the default is 0.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetUpdatedAlarmEventsProcessor`1.PriorityTo">
            <summary>
            Maximum Priority to return.  If not supplied, the default is 1000.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.GetUpdatedAlarmEventsProcessor`1.Types">
            <summary>
            LinkedList of Types derived from the request and set during ValidateRequest.  NULL if none are supplied.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.RenewProcessor.Duration">
            <summary>
            Set by the base class during validation of the request.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RenewProcessor.GetSubscriptionStatus(System.String)">
            <summary>
            Returns the status of the supplied Subscription.  If the subscription is not found or the subscriptionId is invalid, return Invalid.
            </summary>
        </member>
        <member name="T:Ews.Server.Contract.Processor.RequestProcessorBase`2">
            <summary>
            Commong EWS processor base class to scaffold the standard request processing.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.RequestProcessorBase`2.VersionOfRequest">
            <summary>
            Set early in ProcessRequest.  Available for the duration of the call.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.ProcessRequest(`0)">
            <summary>
            Processes the supplied request and returns the response.  The base implementation is to handle logging of request and response.  It is the responsibility of the sub-class to
            implement the core functionality of the processor in the ProcessRequest_Subclass method.  The version of the response is always set by the method depending on the implied 
            VersionOfRequest.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.OnProcessRequestStarted(`0)">
            <summary>
            Called before validation.  Good point to inject start of method logging.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.ValidateRequest(`0)">
            <summary>
            Validates the request and throws SOAP faults as needed.  Any validation that does not need to throw SOAP faults should be done during processing in that it would return
            validation error results and not throw faults.  Handles PERMISSION_DENIED, OPERATION_NOT_SUPPORTED, and METADATA_NOT_SUPPORTED.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.OnProcessRequestEnded(`1,System.TimeSpan)">
            <summary>
            Called immdiately before response is returned.  Good point to inject start of method logging.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertAuthentication">
            <summary>
            Asserts that the current principal has been Authenticated and has the proper claims to call the supplied method.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertOperationIsAllowed">
            <summary>
            Asserts that the requested method is in fact implemented by this Serve implementation.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.IsOperationSupported">
            <summary>
            Returns true if the requested operation is supported in this Serve implementation
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertIsValidRequest(`0)">
            <summary>
            Asserts that the requested is structurally valid
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.IsValidRequest(`0)">
            <summary>
            Returns true if the request is strucurally valid.  By default, will alwasy ensure that the InnerRequest is non-NULL.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertMetadataIsAllowed">
            <summary>
            Asserts that metadata can be served when requested.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertIsValidId(System.String)">
            <summary>
            Asserts that the supplied ID is a valid ID in this Serve implementation for the supplied method call.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.ValidateId(System.String,Ews.Server.Contract.ErrorResultType@)">
            <summary>
            Validates the supplied id is consistant with the method call requested and that the current Principal has access to that point.
            Provides an ErrorResultType if the Id is not valid.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.ValidateId(System.String,Ews.Server.Contract.ResultType@)">
            <summary>
            Validates the supplied id is consistant with the method call requested and that the current Principal has access to that point.
            Provides an ResultTYpe if the Id is not valid.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.IsValidId(System.String)">
            <summary>
            Returns true or false indicating that the supplied ID is valid for the supplied ewsMethodCall
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.AssertUserHasAccess(System.String)">
            <summary>
            Asserts that the supplied ID is a valid ID in this Serve implementation for the supplied method call.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.RequestProcessorBase`2.UserHasAccess(System.String)">
            <summary>
            Returns true if the current Principal has rights to access the supplied point using the method supplied.
            Returns false otherwise
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.SetValuesProcessor.IsValidValue(System.String,System.String,Ews.Server.Contract.ResultType@)">
            <summary>
            Main entry point for value validation.  Validates that the ValueItem exists and that the value is consistant with the ValueItem.  Override overloaded method for implementation specific 
            value validation.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.SetValuesProcessor.IsValidValue(System.String,System.String)">
            <summary>
            Validates the supplied value for the id given.  Test for datatype inconsistancies.  
            At this level, this method will always return true.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.Duration">
            <summary>
            The Duration of the subscription extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.EventType">
            <summary>
            Type of subscription extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.EventMode">
            <summary>
            Mode of the subscription extracted from the request during validation.  Should always be PullEventing but this implementation honors the concept of PushEventing even if it is not 
            implemented.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.IdType">
            <summary>
            How the Ids supplied in the request will be interpretted
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.PriorityFrom">
            <summary>
            Minimum Priority to return extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.PriorityTo">
            <summary>
            Maximum Priority to return extracted from the request during validation.
            </summary>
        </member>
        <member name="P:Ews.Server.Contract.Processor.SubscribeProcessor.Types">
            <summary>
            Types extracted from the request during validation.  NULL if none are supplied.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.UnforceValuesProcessor.IsForced(System.String)">
            <summary>
            Sub-classes must return true if the ValueItem indicated by item is currently in a Forced state.
            Called AFTER item has been validated to be ValueItem Id.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.UnforceValuesProcessor.UnforceValue(System.String)">
            <summary>
            Sub-classes inject logic to actually unforce the ValueItem indicated by item.
            </summary>
        </member>
        <member name="M:Ews.Server.Contract.Processor.UnsubscribeProcessor.GetSubscriptionStatus(System.String)">
            <summary>
            Returns the status of the supplied Subscription.  If the subscription is not found or the subscriptionId is invalid, return Invalid.
            </summary>
        </member>
    </members>
</doc>
