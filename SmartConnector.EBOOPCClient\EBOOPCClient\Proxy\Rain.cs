﻿using System;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x02000014 RID: 20
	public class Rain
	{
		// Token: 0x1700002A RID: 42
		// (get) Token: 0x060000B1 RID: 177 RVA: 0x000082D2 File Offset: 0x000064D2
		// (set) Token: 0x060000B2 RID: 178 RVA: 0x000082DA File Offset: 0x000064DA
		[JsonProperty("3h", NullValueHandling = NullValueHandling.Ignore)]
		public double? The3H { get; set; }
	}
}
