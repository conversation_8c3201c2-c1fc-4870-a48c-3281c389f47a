   .winmd.dll.exe 
   D:\mk\app.configSC:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\AMS.Profile.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Microsoft.CSharp.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\mscorlib.dllWC:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\Newtonsoft.Json.dllWC:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\OpcDaNetUA.Net4.dllWC:\Program Files (x86)\Control-See\EBO-OPC Client\SmartConnectorBIN\OpcNetBase.Net4.dll~C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ComponentModel.DataAnnotations.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Configuration.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ServiceModel.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName},D:\mk\SmartConnector.EBOOPCClient\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}UD:\mk\SmartConnector.EBOOPCClient\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2msil
v4.0.30319         