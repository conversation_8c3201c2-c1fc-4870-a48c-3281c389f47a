<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ews.Common</name>
    </assembly>
    <members>
        <member name="T:Ews.Common.Authentication.AuthCookie">
            <summary>
            Represents an authentication cookie for purposes of optimizing HTTP Digest authentication.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.AuthCookie.CookieId">
            <summary>
            Id of the Cookie itself.  Setter only for XML serialization purposes.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.AuthCookie.UserId">
            <summary>
            Id of the User which has been authenticated using normal means.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.AuthCookie.UserName">
            <summary>
            Name of the User which had been authenticated using normal means.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.AuthCookie.ClientIp">
            <summary>
            IP of the client which authenticated using normal means.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.AuthCookie.CreatedOn">
            <summary>
            When the cookie was originally created.  Setter only for XML serialization purposes.
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.AuthenticationResponse">
            <summary>
            Response from AuthenticateFromHeader
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.ClaimsAuthenticationManagerBase">
            <summary>
            Base class for standard Claims transformation using a ClaimsAuthenticationManager
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.DigestAuthenticationManagerBase">
            <summary>
            ServiceAuthenticationManager subclass for our Digest authentication/authorization purposes.
            Handles responding to the Authorization HTTP header from the client.  DigestAuthorizationManager issues the digest challenge.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestAuthenticationManagerBase.AuthenticateFromCookie(System.String)">
            <summary>
            Subclasses must do this.  The only thing the base class can supply is the cookieHeader.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestAuthenticationManagerBase.GetPassword(System.String)">
            <summary>
            Returns a Tuple of the password and an optional ID for the user object.  It is up to the sub-class to supply this functionality.
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.DigestAuthorizationManagerBase">
            <summary>
            ServiceAuthorizationManager subclass for our Digest authentication/authorization purposes.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestAuthorizationManagerBase.CheckAccess(System.ServiceModel.OperationContext,System.ServiceModel.Channels.Message@)">
            <summary>
            Handles issuing the digest authentication challenge to the client if the DigestAuthenticationManager has not authenticated us yet (presence of the AuthenticatedUsername message property).
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestAuthorizationManagerBase.GenerateAuthCookie(System.String,System.ServiceModel.OperationContext)">
            <summary>
            Generates an AuthCookie instance for the supplied user.
            </summary>
            <returns>
            The intent is for this to be overridden by subclasses so that they can add other information that needs to be stored in the cookie
            </returns>
        </member>
        <member name="M:Ews.Common.Authentication.DigestAuthorizationManagerBase.ExtractMessageProperty(System.ServiceModel.Channels.Message,System.String)">
            <summary>
            Extracts the named message property if it exists and returns it. 
            Returns null if the key is not found.
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.DigestHeaderInfo">
            <summary>
            Represents either the challenge or response header.  Not all pieces will always be present.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Username">
            <summary>
            The name of the user authenticating.  Available in both challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Realm">
            <summary>
            The server's realm .  Available in both challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Nonce">
            <summary>
            The server genreated nonce value.  Available in both challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Opaque">
            <summary>
            The server genreated nonce value.  Available in both challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Nc">
            <summary>
            The client generated "nonce count".  Available in the response only.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Cnonce">
            <summary>
            The client generated nonce value.  Available in the response only.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Uri">
            <summary>
            Path and query parameters of the request made by the client.  Available in the response only.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Qop">
            <summary>
            Quality of protection client applies to the message.  Must be supported by the server.  Available in both challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Algorithm">
            <summary>
            The hashing algorithm used by the server/client.  Available in both the challenge and response.
            </summary>
        </member>
        <member name="P:Ews.Common.Authentication.DigestHeaderInfo.Response">
            <summary>
            The client generated response to the server's authentication challenge.  Available in the response only.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHeaderInfo.Parse(System.String)">
            <summary>
            Parses the raw header value into zero or more instances of this class.  Supports multiple challenges in the header to support RFC7616.  Orders them in descending order
            from most secure (SHA512-256) to least (MD5)
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.DigestHelper">
            <summary>
            Utility class for digest authentication.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.CreateDigestChallenge(Ews.Common.Authentication.HttpDigestHashAlgorithm,System.String,System.Boolean)">
            <summary>
            Generates an HTTP digest challenge.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.CreateDigestResponse(Ews.Common.Authentication.DigestHeaderInfo,System.String,System.String,System.String)">
            <summary>
            Generates the http digest response to the supplied challenge given the password, httpMethod and uri.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.AuthenticateFromHeader(System.String,System.Collections.Generic.List{Ews.Common.Authentication.HttpDigestHashAlgorithm},System.String,System.String,System.Func{System.String,System.Tuple{System.String,System.String}})">
            <summary>
            Performs the http digest authentication from the supplied Authorize header.  We support the realm and algorithms currently set by the server.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.CalculateDigestHash(Ews.Common.Authentication.DigestHeaderInfo,System.String,System.String)">
            <summary>
            Calculates the HTTP digest "hash" value from the header, password, and httpMethod.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.GenerateNonce">
            <summary>
            This implementation will create a nonce which is the text representation of the current time, plus one minute.  The nonce will be valid for this one minute.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.DigestHelper.IsValidNonce(System.String)">
            <summary>
            Validates the supplied nonce value.  If the nonce is valid, it will invalidate it since it should only be usable one time.
            </summary>
        </member>
        <member name="T:Ews.Common.Authentication.HttpDigestHashAlgorithm">
            <summary>
            HttpDigest hashing HashAlgorithm.  Updated to support RFC7616 hashing algorithms.
            </summary>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Md5">
            <summary>
            MD5 hashing HashAlgorithm
            </summary>
            <remarks>Apache Default</remarks>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Md5Session">
            <summary>
            MD5 hashing HashAlgorithm (session variant)
            </summary>
            <remarks>IIS Default</remarks>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Sha256">
            <summary>
            SHA256 hashing HashAlgorithm
            </summary>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Sha256Session">
            <summary>
            SHA256 hashing HashAlgorithm (session variant)
            </summary>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Sha512">
            <summary>
            SHA512-256 hasing HashAlgorithm
            </summary>
        </member>
        <member name="F:Ews.Common.Authentication.HttpDigestHashAlgorithm.Sha512Session">
            <summary>
            SHA512-256 hasing HashAlgorithm (session variant)
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.ExtensionMethods.ToHeaderStringValue(Ews.Common.Authentication.HttpDigestHashAlgorithm)">
            <summary>
            Converts the HttpDigestHashAlgorithm to the useable challenge value.
            </summary>
        </member>
        <member name="M:Ews.Common.Authentication.ExtensionMethods.FromHeaderStringValue(System.String)">
            <summary>
            Parses value and returns an HttpDigestHashAlgorithm value.  See remarks for why we can't use Enum.Parse here.
            </summary>
            <remarks>
            Since what we serialize can't be represented as an enum (due to the hyphen) we have ToHeaderStringValue and this method to do what Enum.Parse can't.
            </remarks>
        </member>
        <member name="M:Ews.Common.Authentication.ExtensionMethods.AsNonceValue(System.String)">
            <summary>
            Returns value suitable for use as a "nonce".
            </summary>
        </member>
        <member name="T:Ews.Common.EwsSubscriptionStatusEnum">
            <summary>
            The status of an EwsSubscription
            </summary>
        </member>
        <member name="F:Ews.Common.EwsSubscriptionStatusEnum.Invalid">
            <summary>
            The subscription does not exist.
            </summary>
        </member>
        <member name="F:Ews.Common.EwsSubscriptionStatusEnum.Active">
            <summary>
            The subscription is curretnly active.
            </summary>
        </member>
        <member name="F:Ews.Common.EwsSubscriptionStatusEnum.Inactive">
            <summary>
            The subscription has expired or was termianted by the subscriber.
            </summary>
        </member>
        <member name="F:Ews.Common.EwsGetHistoryMoreDataRefMode.Normal">
            <summary>
            Default mode as defined in the EWS specification.
            </summary>
        </member>
        <member name="F:Ews.Common.EwsGetHistoryMoreDataRefMode.SequenceNumber">
            <summary>
            Optimized mode for SBO which utilizes MoreDataRef to return HistoryRecords by pointer rather than date.
            </summary>
        </member>
        <member name="T:Ews.Common.AlarmTransitionModel">
            <summary>
            Defines how an AlarmItem's State can transition 
            </summary>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.Unrestricted">
            <summary>
            Any transition is allowed.  This mode will not enforce transitions as all other modes will.
            </summary>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.SimpleSystemAlarm">
            <summary>
            Ack is required to return to Normal from Active state.
            </summary>
            <remarks>
            Normal-Active-[Ack]-Normal
            </remarks>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.NoAcknowledgeRequired">
            <summary>
            Ack is not required to return to Normal from Active state.
            </summary>
            <remarks>
            Normal-Active-[Reset]-Normal
            </remarks>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.SimpleTransientAlarm">
            <summary>
            Ack from Reset will return to normal.
            </summary>
            <remarks>
            Normal-Active-[Reset]-Reset-[Ack]-Normal
            </remarks>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.SingleAcknowledgeRequirement">
            <summary>
            Reset is required to return to Normal from a Acknowledged state.
            Ack is required to return to Normal from a Reset state.
            </summary>
            <remarks>
            Normal-Active-[Reset]-Reset-[Ack]-Normal
                         -[Ack]-Acknowledged-[Reset]-Normal
            </remarks>
        </member>
        <member name="F:Ews.Common.AlarmTransitionModel.ExtendedAcknowledgeRequirement">
            <summary>
            Extends SingleAcknowledgeRequirement alarm by requiring an Ack to return to normal from Reset state.
            </summary>
            <remarks>
            Normal-Active--------------------[Reset]-Reset-[Ack]-Normal
                         -[Ack]-Acknowledged-[Reset]-Reset-[Ack]-Normal
            </remarks>
        </member>
        <member name="T:Ews.Common.EwsConnection">
            <summary>
            Define the properties needed to establish a connection to an EWS Server.
            </summary>
        </member>
        <member name="P:Ews.Common.EwsConnection.EwsEndpoint">
            <inheritdoc />
        </member>
        <member name="P:Ews.Common.EwsConnection.UserName">
            <inheritdoc />
        </member>
        <member name="P:Ews.Common.EwsConnection.Password">
            <inheritdoc />
        </member>
        <member name="P:Ews.Common.EwsConnection.Address">
            <inheritdoc />
        </member>
        <member name="T:Ews.Common.ExtensionMethods">
            <summary>
            Extension methods
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsAggregationGroupEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsAggregationTypeEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsAlarmAcknowledgeableEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsAlarmStateEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsContainerTypeEnum)">
            <summary>
            EWS specification requires this value to be a lower cased string
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsSubscriptionEventTypeEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsValueForceableEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsValueStateEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsValueTypeEnum)">
            <summary>
            EWS specification requires this value to be a lower cased string
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsValueWriteableEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsDataModelTypeEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsEventIdTypeModeEnum)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="M:Ews.Common.ExtensionMethods.ToEwsString(Ews.Common.EwsVersion)">
            <summary>
            EWS specification requires this value to be a string representation of the integer equivalent.
            </summary>
        </member>
        <member name="T:Ews.Common.IEwsEndpoint">
            <summary>
            Defines the common properties and behavior when a connection to an EWS Server endpoint is required.
            </summary>
        </member>
        <member name="P:Ews.Common.IEwsEndpoint.EwsEndpoint">
            <summary>
            Complete URL of the EWS server endpoint.
            </summary>
        </member>
        <member name="P:Ews.Common.IEwsEndpoint.UserName">
            <summary>
            User name to authenticate against.
            </summary>
        </member>
        <member name="P:Ews.Common.IEwsEndpoint.Password">
            <summary>
            Password to authenticate against.
            </summary>
        </member>
        <member name="T:Ews.Common.EwsValueItemValueValidator">
            <summary>
            Stateful EwsValueItem value validator.
            </summary>
        </member>
        <member name="F:Ews.Common.EwsValueItemValueValidator.ValueItemDateFormat">
            <summary>
            Format of DateTime ValueItems when persisted to the database.  By default this is "s".
            </summary>
        </member>
        <member name="M:Ews.Common.EwsValueItemValueValidator.Validate(System.String,Ews.Common.EwsValueTypeEnum)">
            <summary>
            Validates that value is type consistent with the Processor's ValueItem.Type property.  Sets the type safe "AsXXX" property and the NormalizedValue along the way.
            </summary>
            <param name="rawValue">Raw value to validate</param>
            <param name="valueItemType">Type rawValue should be validated against.</param>
            <returns>True/False for validity.</returns>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.IsValid">
            <summary>
            Returns the validation result the last time Validate was called with RawValue and Type as the parameters supplied.
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.NormalizedValue">
            <summary>
            A normalized represetnation of the underlying type specific value.  
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.Type">
            <summary>
            The type supplied to IsValid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsBoolean">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsInteger">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsLong">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsDateTime">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsDouble">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.EwsValueItemValueValidator.AsTimeSpan">
            <summary>
            Typesafe value if valid
            </summary>
        </member>
        <member name="P:Ews.Common.IInformationProvider.PageSize">
            <summary>
            Logical page size for paged operations
            </summary>
        </member>
        <member name="T:Ews.Common.Resources.Glossary">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.AlreadyAcknowledged">
            <summary>
              Looks up a localized string similar to Already acknowledged.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.EventMode">
            <summary>
              Looks up a localized string similar to EventMode.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.EventType">
            <summary>
              Looks up a localized string similar to EventType.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Expiration">
            <summary>
              Looks up a localized string similar to Expires.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.ForcedValue">
            <summary>
              Looks up a localized string similar to Attempt to set forced item.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Group">
            <summary>
              Looks up a localized string similar to AggregationGroup.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.IDList">
            <summary>
              Looks up a localized string similar to ID List.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidID">
            <summary>
              Looks up a localized string similar to Invalid ID.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidMoreDataRefParameter">
            <summary>
              Looks up a localized string similar to Invalid MoreDataRef parameter.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidParameter">
            <summary>
              Looks up a localized string similar to Invalid {0} parameter.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidPriorityParameter">
            <summary>
              Looks up a localized string similar to Invalid Priority parameter.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidRequest">
            <summary>
              Looks up a localized string similar to Invalid request.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidTimeParameter">
            <summary>
              Looks up a localized string similar to Invalid Time parameter.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidType">
            <summary>
              Looks up a localized string similar to Invalid Type.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.InvalidValue">
            <summary>
              Looks up a localized string similar to Invalid Value.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.MetadataNotSupported">
            <summary>
              Looks up a localized string similar to Metadata is not supported.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.MissingParameter">
            <summary>
              Looks up a localized string similar to Missing {0} parameter.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.MoreDataRef">
            <summary>
              Looks up a localized string similar to MoreDataRef.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.NotAcknowledgeable">
            <summary>
              Looks up a localized string similar to Not acknowledgeable.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.NotForceable">
            <summary>
              Looks up a localized string similar to Attempt to modify non-forceable item.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.NotificationId">
            <summary>
              Looks up a localized string similar to NotificationId.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.NotProcessed">
            <summary>
              Looks up a localized string similar to Not processed.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.OperationFailed">
            <summary>
              Looks up a localized string similar to Operation failed.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.OperationNotSupported">
            <summary>
              Looks up a localized string similar to Operation not supported.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.PermissionDenied">
            <summary>
              Looks up a localized string similar to Permission denied.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Priority">
            <summary>
              Looks up a localized string similar to Priority.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.ReadOnly">
            <summary>
              Looks up a localized string similar to Attempt to modify readonly item.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.SubscriptionId">
            <summary>
              Looks up a localized string similar to SubscriptionId.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.SubscriptionTerminated">
            <summary>
              Looks up a localized string similar to The referenced Subscription has been terminated.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Successful">
            <summary>
              Looks up a localized string similar to Success.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Time">
            <summary>
              Looks up a localized string similar to Time.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.Timeout">
            <summary>
              Looks up a localized string similar to Timeout.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.UnequalLists">
            <summary>
              Looks up a localized string similar to Unequal List parameters.
            </summary>
        </member>
        <member name="P:Ews.Common.Resources.Glossary.UnforcedValue">
            <summary>
              Looks up a localized string similar to Attempt to unforce non-forced item.
            </summary>
        </member>
    </members>
</doc>
