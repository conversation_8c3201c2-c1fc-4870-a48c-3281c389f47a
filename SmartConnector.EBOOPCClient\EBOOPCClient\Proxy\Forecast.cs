﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SmartConnector.EBOOPCClient.Proxy
{
	// Token: 0x0200000E RID: 14
	public class Forecast
	{
		// Token: 0x1700000D RID: 13
		// (get) Token: 0x06000071 RID: 113 RVA: 0x000080DD File Offset: 0x000062DD
		// (set) Token: 0x06000072 RID: 114 RVA: 0x000080E5 File Offset: 0x000062E5
		[JsonProperty("cod")]
		public string Cod { get; set; }

		// Token: 0x1700000E RID: 14
		// (get) Token: 0x06000073 RID: 115 RVA: 0x000080EE File Offset: 0x000062EE
		// (set) Token: 0x06000074 RID: 116 RVA: 0x000080F6 File Offset: 0x000062F6
		[JsonProperty("message")]
		public double Message { get; set; }

		// Token: 0x1700000F RID: 15
		// (get) Token: 0x06000075 RID: 117 RVA: 0x000080FF File Offset: 0x000062FF
		// (set) Token: 0x06000076 RID: 118 RVA: 0x00008107 File Offset: 0x00006307
		[JsonProperty("cnt")]
		public long Cnt { get; set; }

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x06000077 RID: 119 RVA: 0x00008110 File Offset: 0x00006310
		// (set) Token: 0x06000078 RID: 120 RVA: 0x00008118 File Offset: 0x00006318
		[JsonProperty("list")]
		public List<List> List { get; set; }

		// Token: 0x17000011 RID: 17
		// (get) Token: 0x06000079 RID: 121 RVA: 0x00008121 File Offset: 0x00006321
		// (set) Token: 0x0600007A RID: 122 RVA: 0x00008129 File Offset: 0x00006329
		[JsonProperty("city")]
		public City City { get; set; }
	}
}
