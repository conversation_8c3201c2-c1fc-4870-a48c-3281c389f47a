using System;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	/// <summary>
	/// License management utility class
	/// </summary>
	public static class LicenseManager
	{
		/// <summary>
		/// Check if the application is currently licensed
		/// </summary>
		/// <returns>True if licensed, false otherwise</returns>
		public static bool IsLicensed()
		{
			try
			{
				// Check if license info is already cached
				if (Global._LicInfo != null)
				{
					// Check if license is still valid
					if (!Global._LicInfo.m_bDemo)
					{
						return true; // Valid license
					}
					
					if (Global._LicInfo.m_ExpertionUnlimited)
					{
						return true; // Unlimited license
					}
					
					// Check if time-limited license is still valid
					return DateTime.Now < Global._LicInfo.m_dtExpierd;
				}
				
				// License info not cached, perform fresh check
				UCMECheckLicense.UCMELicInfo licInfo;
				int result = UCMECheckLicense.CheckLicense(out licInfo);
				
				if (result == 0)
				{
					Global._LicInfo = licInfo;
					
					if (!licInfo.m_bDemo)
					{
						return true; // Valid license
					}
					
					if (licInfo.m_ExpertionUnlimited)
					{
						return true; // Unlimited license
					}
					
					// Check if time-limited license is still valid
					return DateTime.Now < licInfo.m_dtExpierd;
				}
				
				return false;
			}
			catch (Exception)
			{
				return false;
			}
		}
		
		/// <summary>
		/// Get license information
		/// </summary>
		/// <returns>License information or null if not licensed</returns>
		public static UCMECheckLicense.UCMELicInfo GetLicenseInfo()
		{
			try
			{
				if (Global._LicInfo != null)
				{
					return Global._LicInfo;
				}
				
				UCMECheckLicense.UCMELicInfo licInfo;
				int result = UCMECheckLicense.CheckLicense(out licInfo);
				
				if (result == 0)
				{
					Global._LicInfo = licInfo;
					return licInfo;
				}
				
				return null;
			}
			catch (Exception)
			{
				return null;
			}
		}
		
		/// <summary>
		/// Get license status message
		/// </summary>
		/// <returns>Human-readable license status</returns>
		public static string GetLicenseStatusMessage()
		{
			try
			{
				var licInfo = GetLicenseInfo();
				if (licInfo == null)
				{
					return "No valid license found";
				}
				
				if (!licInfo.m_bDemo)
				{
					return $"Licensed (Version: {licInfo.m_nVer})";
				}
				
				if (licInfo.m_ExpertionUnlimited)
				{
					return $"Unlimited License (Version: {licInfo.m_nVer})";
				}
				
				if (DateTime.Now < licInfo.m_dtExpierd)
				{
					return $"Licensed until {licInfo.m_dtExpierd.ToShortDateString()} (Version: {licInfo.m_nVer})";
				}
				
				return $"License expired on {licInfo.m_dtExpierd.ToShortDateString()}";
			}
			catch (Exception ex)
			{
				return $"License check error: {ex.Message}";
			}
		}
		
		/// <summary>
		/// Get days remaining until license expires
		/// </summary>
		/// <returns>Days remaining, -1 if unlimited, -2 if expired or invalid</returns>
		public static int GetDaysRemaining()
		{
			try
			{
				var licInfo = GetLicenseInfo();
				if (licInfo == null || licInfo.m_bDemo)
				{
					return -2; // Invalid or expired
				}
				
				if (licInfo.m_ExpertionUnlimited)
				{
					return -1; // Unlimited
				}
				
				var timeSpan = licInfo.m_dtExpierd - DateTime.Now;
				return Math.Max(0, (int)timeSpan.TotalDays);
			}
			catch (Exception)
			{
				return -2;
			}
		}
		
		/// <summary>
		/// Force refresh of license information
		/// </summary>
		/// <returns>True if license is valid after refresh</returns>
		public static bool RefreshLicense()
		{
			try
			{
				Global._LicInfo = null; // Clear cached info
				return IsLicensed();
			}
			catch (Exception)
			{
				return false;
			}
		}
	}
}
