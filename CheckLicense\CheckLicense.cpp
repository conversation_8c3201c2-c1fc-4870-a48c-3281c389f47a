// CheckLicense.cpp : Defines the exported functions for the DLL application.
//

#include <windows.h>
#include <time.h>

// Export the CheckLicense function
extern "C" __declspec(dllexport) int CheckLicense(int* dd, int* mm, int* yy, int* ver);

// Simple license checking function
// This is a sample implementation - in production this would contain
// proper license validation logic with encryption/decryption
int CheckLicense(int* dd, int* mm, int* yy, int* ver)
{
    if (!dd || !mm || !yy || !ver)
        return -1; // Invalid parameters

    try
    {
        // Sample license logic:
        // For demonstration purposes, we'll return a license that expires 30 days from now
        // In production, this would read from a license file or registry
        
        time_t now;
        time(&now);
        
        struct tm* timeinfo = localtime(&now);
        
        // Add 30 days to current date for demo license
        timeinfo->tm_mday += 30;
        mktime(timeinfo); // Normalize the date
        
        *dd = timeinfo->tm_mday;
        *mm = timeinfo->tm_mon + 1; // tm_mon is 0-based
        *yy = timeinfo->tm_year + 1900;
        *ver = 1; // License version
        
        // For unlimited license, return -1 for all date values:
        // *dd = -1;
        // *mm = -1;
        // *yy = -1;
        // *ver = 1;
        
        return 0; // Success
    }
    catch (...)
    {
        return -1; // Error
    }
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}
