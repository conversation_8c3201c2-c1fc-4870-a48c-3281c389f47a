﻿using System;
using System.Collections.Generic;
using Ews.Server.Contract;
using Mongoose.Common;
using Mongoose.Ews.Server.Data;

namespace SmartConnector.EBOOPCClient.EwsServer
{
	// Token: 0x0200001A RID: 26
	public class CustomEwsServiceHost : EwsServiceHost
	{
		// Token: 0x060000E0 RID: 224 RVA: 0x00008435 File Offset: 0x00006635
		public CustomEwsServiceHost(EwsServer serverConfiguration) : base(typeof(CustomDataExchange), serverConfiguration)
		{
		}

		// Token: 0x060000E3 RID: 227 RVA: 0x00008457 File Offset: 0x00006657
		protected override void ProvisionEndpoint()
		{
			base.AddServiceEndpoint(typeof(IDataExchange), EwsServiceHost.CreateBinding(base.IsHttps), base.ServerAddress);
		}
	}
}
