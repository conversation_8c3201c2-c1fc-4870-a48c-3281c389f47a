﻿using System;
using System.Collections.Generic;
using Ews.Server.Contract;
using Mongoose.Common;
using Mongoose.Ews.Server.Data;
using SxL.Common;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient.EwsServer
{
	// Token: 0x0200001A RID: 26
	public class CustomEwsServiceHost : EwsServiceHost
	{
		// Token: 0x060000E0 RID: 224 RVA: 0x00008435 File Offset: 0x00006635
		public CustomEwsServiceHost(Mongoose.Ews.Server.Data.EwsServer serverConfiguration) : base(typeof(CustomDataExchange), serverConfiguration)
		{
		}

		// Token: 0x060000E3 RID: 227 RVA: 0x00008457 File Offset: 0x00006657
		protected override void ProvisionEndpoint()
		{
			base.AddServiceEndpoint(typeof(IDataExchange), EwsServiceHost.CreateBinding(base.IsHttps), base.ServerAddress);
		}

		public override bool IsLicensed
		{
			get { return true; } // Enable licensing requirement
		}

		public override IEnumerable<Prompt> ValidateCustomLicenseFeatures(Mongoose.Common.Data.Licensing.ExtensionLicense license)
		{
			var prompts = new List<Prompt>();

			try
			{
				// Check UCME license
				UCMECheckLicense.UCMELicInfo licInfo;
				int result = UCMECheckLicense.CheckLicense(out licInfo);

				if (result != 0)
				{
					prompts.Add(new Prompt
					{
						Message = "EWS Service license validation failed. Please ensure you have a valid license.",
						PromptType = PromptTypes.Error
					});
				}
				else if (licInfo.m_bDemo)
				{
					if (!licInfo.m_ExpertionUnlimited && DateTime.Now >= licInfo.m_dtExpierd)
					{
						prompts.Add(new Prompt
						{
							Message = $"EWS Service license has expired on {licInfo.m_dtExpierd.ToShortDateString()}. Please renew your license.",
							PromptType = PromptTypes.Error
						});
					}
					else if (!licInfo.m_ExpertionUnlimited)
					{
						prompts.Add(new Prompt
						{
							Message = $"EWS Service license will expire on {licInfo.m_dtExpierd.ToShortDateString()}.",
							PromptType = PromptTypes.Warning
						});
					}
				}

				// Store license info globally for other components to use
				Global._LicInfo = licInfo;
			}
			catch (Exception ex)
			{
				prompts.Add(new Prompt
				{
					Message = $"EWS Service license validation error: {ex.Message}",
					PromptType = PromptTypes.Error
				});
			}

			return prompts;
		}
	}
}
